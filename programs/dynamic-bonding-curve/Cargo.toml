[package]
name = "dynamic-bonding-curve"
version = "0.1.6"
description = "Created with Anchor"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]
name = "dynamic_bonding_curve"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = []
local = []
idl-build = ["anchor-lang/idl-build", "anchor-spl/idl-build"]

[dependencies]
anchor-lang = { workspace = true, features = ["event-cpi"] }
anchor-spl = { workspace = true, features = [] }
const-crypto = "0.3.0"
bytemuck = { workspace = true, features = ["min_const_generics"] }
static_assertions = "1.1.0"
ruint =  { workspace = true }
num-traits = "0.2.19"
num_enum = "0.7.0"
num = "0.4.3"
mpl-token-metadata = "5.1.0"
spl-token-metadata-interface = "0.6"
dynamic-amm = { path = "../../libs/dynamic-amm" }
damm-v2 = { path = "../../libs/damm-v2" }
locker = { path = "../../libs/locker" }

[dev-dependencies]
proptest = "1.2.0"
rand = "0.9.2"