In virtual_pool.rs

[H-03] Front-Running and Manipulation of Migration via Swaps
Description
Migration is triggered when quote_reserve >= migration_threshold (virtual). An attacker can front-run a migration transaction by swapping base-to-quote (decreasing quote_reserve below threshold via apply_swap_result), causing the migration to fail.  exploiting race conditions in permissionless migrations.

Impact

Repeated DoS on migration, delaying or preventing pool creation on Raydium, affecting liquidity and user funds.
Affected Code
```rust
pub fn apply_swap_result(
    &mut self,
    // ...
    trade_direction: TradeDirection,
    // ...
) -> Result<()> {
    // Updates quote_reserve without migration locks
    if trade_direction == TradeDirection::BaseToQuote {
        self.quote_reserve = self.quote_reserve.safe_sub(actual_output_amount)?;
    } else {
        self.quote_reserve = self.quote_reserve.safe_add(actual_input_amount)?;
    }

}
```
Reference: apply_swap_result, is_curve_complete.