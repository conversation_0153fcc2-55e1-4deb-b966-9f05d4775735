use crate::{
    swap::{ProcessSwapParams, ProcessSwapResult},
    PoolError, SwapParameters,
};
use anchor_lang::prelude::*;

pub fn process_swap_exact_in(params: ProcessSwapParams<'_>) -> Result<ProcessSwapResult> {
    let ProcessSwapParams {
        amount_0: amount_in,
        amount_1: minimum_amount_out,
        pool,
        config,
        fee_mode,
        trade_direction,
        current_point,
        ..
    } = params;

    let swap_result = pool.get_swap_result_from_exact_input(
        config,
        amount_in,
        fee_mode,
        trade_direction,
        current_point,
    )?;

    require!(
        swap_result.amount_left <= config.get_max_swallow_quote_amount()?,
        PoolError::SwapAmountIsOverAThreshold
    );

    // For BUY operations: compare net after-fee amount
    // When fees are on output, the output_amount is already net after fees
    // When fees are on input, the output_amount is gross before fees are deducted from input
    let net_output_amount = if fee_mode.fees_on_input {
        swap_result.output_amount
    } else {
        // Fees are deducted from output, so output_amount is already net
        swap_result.output_amount
    };

    require!(
        net_output_amount >= minimum_amount_out,
        PoolError::ExceededSlippage
    );

    Ok(ProcessSwapResult {
        swap_result,
        swap_in_parameters: SwapParameters {
            amount_in,
            minimum_amount_out,
        },
    })
}
