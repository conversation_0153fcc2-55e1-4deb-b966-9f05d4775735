use crate::{
    constants::{MAX_SQRT_PRICE, MAX_CURVE_POINT},
    params::liquidity_distribution::LiquidityDistributionParameters,
    safe_math::SafeMath,
    state::{
        fee::VolatilityTracker,
        CollectFeeMode, LiquidityDistributionConfig, PoolConfig, VirtualPool,
    },
};
use anchor_lang::prelude::Pubkey;

/// Test for Donation Attack Vulnerability described in issue.md
/// 
/// Vulnerability: Attackers can donate quote tokens directly to quote_vault,
/// which increases actual vault balance but doesn't update virtual quote_reserve.
/// During migration, only virtual amounts are transferred, leaving donated funds locked.

fn create_pool_config_for_donation_test(migration_threshold: u64) -> PoolConfig {
    let curve = vec![LiquidityDistributionParameters {
        sqrt_price: MAX_SQRT_PRICE,
        liquidity: 1_000_000_000_000_000_000_000_000u128
            .checked_shl(64)
            .unwrap(),
    }];

    let mut config = PoolConfig {
        migration_quote_threshold: migration_threshold,
        migration_fee_percentage: 5, // 5% migration fee
        migration_base_threshold: 1_000_000_000_000, // 1M base tokens
        sqrt_start_price: 1u128 << 64, // 1:1 price ratio
        collect_fee_mode: CollectFeeMode::OutputToken.into(),
        ..Default::default()
    };

    let curve_length = curve.len();
    for i in 0..MAX_CURVE_POINT {
        if i < curve_length {
            config.curve[i] = curve[i].to_liquidity_distribution_config();
        } else {
            config.curve[i] = LiquidityDistributionConfig {
                sqrt_price: MAX_SQRT_PRICE,
                liquidity: 0,
            }
        }
    }
    config
}

fn create_virtual_pool_for_donation_test() -> VirtualPool {
    let mut pool = VirtualPool::default();
    pool.initialize(
        VolatilityTracker::default(),
        Pubkey::default(), // config
        Pubkey::default(), // creator
        Pubkey::default(), // base_mint
        Pubkey::default(), // base_vault
        Pubkey::default(), // quote_vault
        1u128 << 64,       // sqrt_price
        0,                 // activation_point
        0,                 // protocol_base_fee
        1_000_000_000_000_000, // Large base reserve
    );
    pool
}

#[test]
fn test_donation_attack_vulnerability_basic_scenario() {
    println!("=== DONATION ATTACK VULNERABILITY POC ===");
    println!("Testing scenario from issue.md:");
    println!("1. Virtual quote_reserve = 99 SOL (below threshold of 100 SOL)");
    println!("2. Attacker donates 2 SOL to quote_vault");
    println!("3. Legitimate swap pushes virtual to 100 SOL, triggering migration");
    println!("4. Migration transfers only 100 SOL (virtual), leaving 1 SOL excess locked");
    
    // Setup: Migration threshold of 100 SOL
    let migration_threshold = 100_000_000; // 100 SOL in lamports
    let config = create_pool_config_for_donation_test(migration_threshold);
    let mut pool = create_virtual_pool_for_donation_test();
    
    // Initial state: Virtual quote_reserve = 99 SOL (below threshold)
    pool.quote_reserve = 99_000_000; // 99 SOL
    
    println!("\n--- INITIAL STATE ---");
    println!("Virtual quote_reserve: {} lamports (99 SOL)", pool.quote_reserve);
    println!("Migration threshold: {} lamports (100 SOL)", migration_threshold);
    println!("Pool is_curve_complete: {}", pool.is_curve_complete(migration_threshold));
    
    // Verify pool is not ready for migration
    assert!(!pool.is_curve_complete(migration_threshold), 
        "Pool should not be ready for migration initially");
    
    // ATTACK: Attacker donates 2 SOL directly to quote_vault
    let donated_amount = 2_000_000; // 2 SOL
    let simulated_actual_vault_balance = pool.quote_reserve + donated_amount;
    
    println!("\n--- DONATION ATTACK ---");
    println!("Attacker donates {} lamports (2 SOL) directly to quote_vault", donated_amount);
    println!("Actual vault balance after donation: {} lamports (101 SOL)", simulated_actual_vault_balance);
    println!("Virtual quote_reserve (UNCHANGED): {} lamports (99 SOL)", pool.quote_reserve);
    
    // Key vulnerability: Virtual reserves unchanged by direct donations
    assert_eq!(pool.quote_reserve, 99_000_000, 
        "Virtual quote_reserve should be unchanged by direct donation");
    
    // Legitimate swap pushes virtual reserve over threshold
    let legitimate_swap = 1_000_000; // 1 SOL
    pool.quote_reserve = pool.quote_reserve.safe_add(legitimate_swap).unwrap();
    let total_actual_vault_balance = simulated_actual_vault_balance + legitimate_swap;
    
    println!("\n--- LEGITIMATE SWAP TRIGGERS MIGRATION ---");
    println!("Legitimate user swaps {} lamports (1 SOL)", legitimate_swap);
    println!("Virtual quote_reserve after swap: {} lamports (100 SOL)", pool.quote_reserve);
    println!("Total actual vault balance: {} lamports (102 SOL)", total_actual_vault_balance);
    println!("Pool is_curve_complete: {}", pool.is_curve_complete(migration_threshold));
    
    // Migration is now triggered
    assert!(pool.is_curve_complete(migration_threshold), 
        "Pool should be ready for migration after legitimate swap");
    
    // Calculate migration amounts (this is where the vulnerability manifests)
    let migration_amount = config.get_migration_quote_amount_for_config().unwrap();
    
    println!("\n--- MIGRATION CALCULATION ---");
    println!("Quote amount to be migrated: {} lamports ({} SOL)", 
        migration_amount.quote_amount, migration_amount.quote_amount / 1_000_000);
    println!("Migration fee: {} lamports", migration_amount.fee);
    
    // VULNERABILITY IMPACT: Calculate locked funds
    let funds_locked = total_actual_vault_balance.saturating_sub(migration_amount.quote_amount);
    
    println!("\n--- VULNERABILITY IMPACT ---");
    println!("Total actual vault balance: {} lamports", total_actual_vault_balance);
    println!("Amount migrated (based on virtual): {} lamports", migration_amount.quote_amount);
    println!("FUNDS LOCKED/LOST: {} lamports ({} SOL)", funds_locked, funds_locked / 1_000_000);
    
    // CRITICAL ASSERTIONS
    assert!(funds_locked > 0, 
        "VULNERABILITY CONFIRMED: {} lamports will be locked due to donation attack", 
        funds_locked);
    
    // The locked amount should be close to the donated amount
    assert!(funds_locked >= donated_amount / 2, 
        "Significant portion of donated funds should be locked");
    
    println!("\n✅ DONATION ATTACK VULNERABILITY CONFIRMED");
    println!("   Attack successful: {} SOL permanently locked in vault", funds_locked / 1_000_000);
    println!("   Root cause: is_curve_complete() only checks virtual reserves");
    println!("   Impact: Donated funds cannot be recovered through normal means");
}

#[test]
fn test_donation_attack_multiple_donations() {
    println!("=== MULTIPLE DONATIONS ATTACK POC ===");
    
    let migration_threshold = 50_000_000; // 50 SOL
    let config = create_pool_config_for_donation_test(migration_threshold);
    let mut pool = create_virtual_pool_for_donation_test();
    
    // Pool starts at 45 SOL (5 SOL below threshold)
    pool.quote_reserve = 45_000_000;
    
    println!("Initial virtual quote_reserve: {} SOL", pool.quote_reserve / 1_000_000);
    
    // Multiple attackers donate in sequence
    let donation1 = 2_000_000; // 2 SOL
    let donation2 = 3_000_000; // 3 SOL
    let donation3 = 1_000_000; // 1 SOL
    let total_donations = donation1 + donation2 + donation3;
    
    println!("Multiple donations: {} + {} + {} = {} SOL", 
        donation1 / 1_000_000, donation2 / 1_000_000, 
        donation3 / 1_000_000, total_donations / 1_000_000);
    
    let simulated_vault_balance = pool.quote_reserve + total_donations;
    
    // Legitimate swap triggers migration
    let trigger_swap = 6_000_000; // 6 SOL
    pool.quote_reserve = pool.quote_reserve.safe_add(trigger_swap).unwrap();
    let total_vault_balance = simulated_vault_balance + trigger_swap;
    
    println!("Trigger swap: {} SOL", trigger_swap / 1_000_000);
    println!("Virtual reserves after swap: {} SOL", pool.quote_reserve / 1_000_000);
    
    assert!(pool.is_curve_complete(migration_threshold));
    
    // Calculate impact
    let migration_amount = config.get_migration_quote_amount_for_config().unwrap();
    let funds_locked = total_vault_balance.saturating_sub(migration_amount.quote_amount);
    
    println!("Total vault balance: {} SOL", total_vault_balance / 1_000_000);
    println!("Amount migrated: {} SOL", migration_amount.quote_amount / 1_000_000);
    println!("TOTAL FUNDS LOCKED: {} SOL", funds_locked / 1_000_000);
    
    assert!(funds_locked >= total_donations / 2, 
        "Multiple donations should result in significant locked funds");
    
    println!("✅ MULTIPLE DONATIONS ATTACK CONFIRMED: {} SOL locked", 
        funds_locked / 1_000_000);
}

#[test]
fn test_donation_attack_realistic_economic_impact() {
    println!("=== REALISTIC ECONOMIC IMPACT POC ===");
    
    // Realistic production-scale values
    let migration_threshold = 85_000_000_000; // 85,000 SOL
    let config = create_pool_config_for_donation_test(migration_threshold);
    let mut pool = create_virtual_pool_for_donation_test();
    
    // Pool at 84,000 SOL (1,000 SOL below threshold)
    pool.quote_reserve = 84_000_000_000_000;
    
    // Large-scale attack: 500 SOL donation
    let donated_amount = 500_000_000_000; // 500 SOL
    let simulated_vault_balance = pool.quote_reserve + donated_amount;
    
    // Large legitimate swap triggers migration
    let trigger_swap = 1_200_000_000_000; // 1,200 SOL
    pool.quote_reserve = pool.quote_reserve.safe_add(trigger_swap).unwrap();
    
    println!("Scale: {} SOL threshold", migration_threshold / 1_000_000_000);
    println!("Donation: {} SOL", donated_amount / 1_000_000_000);
    println!("Trigger swap: {} SOL", trigger_swap / 1_000_000_000);
    
    assert!(pool.is_curve_complete(migration_threshold));
    
    let migration_amount = config.get_migration_quote_amount_for_config().unwrap();
    let total_vault_balance = simulated_vault_balance + trigger_swap;
    let funds_locked = total_vault_balance.saturating_sub(migration_amount.quote_amount);
    
    println!("Funds locked: {} SOL", funds_locked / 1_000_000_000);
    
    // Economic impact calculation (assuming $100/SOL)
    let economic_impact_usd = (funds_locked / 1_000_000_000) * 100;
    println!("Economic impact: ~${} USD", economic_impact_usd);
    
    assert!(funds_locked >= donated_amount / 2);
    assert!(economic_impact_usd >= 25_000); // At least $25k impact
    
    println!("✅ REALISTIC ATTACK: ${} USD locked permanently", economic_impact_usd);
}
