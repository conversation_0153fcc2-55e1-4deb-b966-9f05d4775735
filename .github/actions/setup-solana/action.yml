name: "Setup Solana"
description: "Setup Solana"
runs:
  using: "composite"
  steps:
    - uses: actions/cache@v4
      name: Cache Solana Tool Suite
      id: cache-solana
      with:
        path: |
          ~/.cache/solana/
          ~/.local/share/solana/
        key: solana-${{ runner.os }}-v0000-${{ env.SOLANA_CLI_VERSION }}
    - run: sh -c "$(curl -sSfL https://release.anza.xyz/v${{ env.SOLANA_CLI_VERSION }}/install)"
      shell: bash
    - run: echo "$HOME/.local/share/solana/install/active_release/bin/" >> $GITHUB_PATH
      shell: bash
    - run: solana-keygen new --no-bip39-passphrase
      shell: bash
    - run: solana config set --url localhost
      shell: bash
