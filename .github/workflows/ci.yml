name: DYNAMIC_BONDING_CURVE

on:
  pull_request:
    branches:
      - main
      - release_0.1.6

env:
  SOLANA_CLI_VERSION: 2.1.0
  NODE_VERSION: 20.19.0
  ANCHOR_CLI_VERSION: 0.31.0  
  TOOLCHAIN: 1.76.0

jobs:
  program_changed_files:
    runs-on: ubuntu-latest
    outputs:
      program: ${{steps.changed-files-specific.outputs.any_changed}}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get specific changed files
        id: changed-files-specific
        uses: tj-actions/changed-files@v18.6
        with:
          files: |
            programs/dynamic-bonding-curve

  anchor_build:
    runs-on: ubuntu-latest
    needs: program_changed_files
    if: needs.program_changed_files.outputs.program == 'true'
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - uses: ./.github/actions/setup-solana
      - uses: ./.github/actions/setup-dep
      - uses: ./.github/actions/setup-anchor
      - uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: ${{ env.TOOLCHAIN }}
          components: clippy
      # Cache rust, cargo
      - uses: Swatinem/rust-cache@v2
        with:
          cache-targets: "true"
          cache-on-failure: true
      - uses: actions/cache@v4
        with:
          path: ~/.anchor
          key: anchor-${{ runner.os }}-${{ hashFiles('Anchor.toml') }}
      - run: anchor build
        shell: bash

  unit_test:
    runs-on: ubuntu-latest
    needs: program_changed_files
    if: needs.program_changed_files.outputs.program == 'true'
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: ${{ env.TOOLCHAIN }}
          components: clippy
      # Cache rust, cargo
      - uses: Swatinem/rust-cache@v2
      - run: cargo test --package dynamic-bonding-curve
        shell: bash
      - run: cargo test --package dynamic-bonding-curve-sdk
        shell: bash

  integration_test:
    runs-on: ubuntu-latest
    needs: program_changed_files
    if: needs.program_changed_files.outputs.program == 'true'
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-solana
      - uses: ./.github/actions/setup-dep
      - uses: ./.github/actions/setup-anchor
      # Install pnpm
      - uses: pnpm/action-setup@v3 # docs https://pnpm.io/continuous-integration#github-actions
        with:
          version: 9.5.0 # Optional: specify a pnpm version

      # Install nightly toolchains
      - uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: ${{ env.TOOLCHAIN }}
          components: clippy    
      # Cache rust, cargo
      - uses: Swatinem/rust-cache@v2

      # Cache node_modules
      - uses: actions/cache@v4
        id: cache-node-modules
        with:
          path: ./node_modules
          key: ${{ runner.os }}-${{ hashFiles('./package-lock.json') }}
      - run: which anchor
        shell: bash
      # Testing
      - run: pnpm install
        shell: bash
      # Run tests with nightly toolchain
      - run: pnpm test
        shell: bash