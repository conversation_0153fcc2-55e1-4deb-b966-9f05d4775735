{"address": "dbcij3LWUppWqq96dh6gJWwBifmcGfLSB5D4DuSMaqN", "metadata": {"name": "dynamic_bonding_curve", "version": "0.1.3", "spec": "0.1.0", "description": "Created with <PERSON><PERSON>"}, "instructions": [{"name": "claim_creator_trading_fee", "discriminator": [82, 220, 250, 189, 3, 85, 107, 45], "accounts": [{"name": "pool_authority", "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "pool", "writable": true}, {"name": "token_a_account", "docs": ["The treasury token a account"], "writable": true}, {"name": "token_b_account", "docs": ["The treasury token b account"], "writable": true}, {"name": "base_vault", "docs": ["The vault token account for input token"], "writable": true, "relations": ["pool"]}, {"name": "quote_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["pool"]}, {"name": "base_mint", "docs": ["The mint of token a"], "relations": ["pool"]}, {"name": "quote_mint", "docs": ["The mint of token b"]}, {"name": "creator", "signer": true, "relations": ["pool"]}, {"name": "token_base_program", "docs": ["Token a program"]}, {"name": "token_quote_program", "docs": ["Token b program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "max_base_amount", "type": "u64"}, {"name": "max_quote_amount", "type": "u64"}]}, {"name": "claim_protocol_fee", "discriminator": [165, 228, 133, 48, 99, 249, 255, 33], "accounts": [{"name": "pool_authority", "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "config", "relations": ["pool"]}, {"name": "pool", "writable": true}, {"name": "base_vault", "docs": ["The vault token account for input token"], "writable": true, "relations": ["pool"]}, {"name": "quote_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["pool"]}, {"name": "base_mint", "docs": ["The mint of token a"], "relations": ["pool"]}, {"name": "quote_mint", "docs": ["The mint of token b"], "relations": ["config"]}, {"name": "token_base_account", "docs": ["The treasury token a account"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [48, 9, 89, 123, 106, 114, 131, 251, 50, 173, 254, 250, 10, 80, 160, 84, 143, 100, 81, 249, 134, 112, 30, 213, 50, 166, 239, 78, 53, 175, 188, 85]}, {"kind": "account", "path": "token_base_program"}, {"kind": "account", "path": "base_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "token_quote_account", "docs": ["The treasury token b account"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [48, 9, 89, 123, 106, 114, 131, 251, 50, 173, 254, 250, 10, 80, 160, 84, 143, 100, 81, 249, 134, 112, 30, 213, 50, 166, 239, 78, 53, 175, 188, 85]}, {"kind": "account", "path": "token_quote_program"}, {"kind": "account", "path": "quote_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "claim_fee_operator", "docs": ["Claim fee operator"]}, {"name": "operator", "docs": ["Operator"], "signer": true, "relations": ["claim_fee_operator"]}, {"name": "token_base_program", "docs": ["Token a program"]}, {"name": "token_quote_program", "docs": ["Token b program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "claim_trading_fee", "discriminator": [8, 236, 89, 49, 152, 125, 177, 81], "accounts": [{"name": "pool_authority", "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "config", "relations": ["pool"]}, {"name": "pool", "writable": true}, {"name": "token_a_account", "docs": ["The treasury token a account"], "writable": true}, {"name": "token_b_account", "docs": ["The treasury token b account"], "writable": true}, {"name": "base_vault", "docs": ["The vault token account for input token"], "writable": true, "relations": ["pool"]}, {"name": "quote_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["pool"]}, {"name": "base_mint", "docs": ["The mint of token a"], "relations": ["pool"]}, {"name": "quote_mint", "docs": ["The mint of token b"], "relations": ["config"]}, {"name": "fee_claimer", "signer": true, "relations": ["config"]}, {"name": "token_base_program", "docs": ["Token a program"]}, {"name": "token_quote_program", "docs": ["Token b program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "max_amount_a", "type": "u64"}, {"name": "max_amount_b", "type": "u64"}]}, {"name": "close_claim_fee_operator", "discriminator": [38, 134, 82, 216, 95, 124, 17, 99], "accounts": [{"name": "claim_fee_operator", "writable": true}, {"name": "rent_receiver", "writable": true}, {"name": "admin", "signer": true}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "create_claim_fee_operator", "docs": ["ADMIN FUNCTIONS ///"], "discriminator": [169, 62, 207, 107, 58, 187, 162, 109], "accounts": [{"name": "claim_fee_operator", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 102, 95, 111, 112, 101, 114, 97, 116, 111, 114]}, {"kind": "account", "path": "operator"}]}}, {"name": "operator"}, {"name": "admin", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "create_config", "discriminator": [201, 207, 243, 114, 75, 111, 47, 189], "accounts": [{"name": "config", "writable": true, "signer": true}, {"name": "fee_claimer"}, {"name": "leftover_receiver"}, {"name": "quote_mint", "docs": ["quote mint"]}, {"name": "payer", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "config_parameters", "type": {"defined": {"name": "ConfigParameters"}}}]}, {"name": "create_locker", "docs": ["PERMISSIONLESS FUNCTIONS ///", "create locker"], "discriminator": [167, 90, 137, 154, 75, 47, 17, 84], "accounts": [{"name": "virtual_pool", "docs": ["Virtual pool"], "writable": true}, {"name": "config", "docs": ["Config"], "relations": ["virtual_pool"]}, {"name": "pool_authority", "writable": true, "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "base_vault", "writable": true, "relations": ["virtual_pool"]}, {"name": "base_mint", "writable": true, "relations": ["virtual_pool"]}, {"name": "base", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [98, 97, 115, 101, 95, 108, 111, 99, 107, 101, 114]}, {"kind": "account", "path": "virtual_pool"}]}}, {"name": "creator", "relations": ["virtual_pool"]}, {"name": "escrow", "writable": true}, {"name": "escrow_token", "writable": true}, {"name": "payer", "writable": true, "signer": true}, {"name": "token_program"}, {"name": "locker_program", "address": "LocpQgucEQHbqNABEYvBvwoxCPsSbG91A1QaQhQQqjn"}, {"name": "locker_event_authority"}, {"name": "system_program", "docs": ["System program."], "address": "11111111111111111111111111111111"}], "args": []}, {"name": "create_partner_metadata", "docs": ["PARTNER FUNCTIONS ////"], "discriminator": [192, 168, 234, 191, 188, 226, 227, 255], "accounts": [{"name": "partner_metadata", "docs": ["Partner metadata"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 97, 114, 116, 110, 101, 114, 95, 109, 101, 116, 97, 100, 97, 116, 97]}, {"kind": "account", "path": "fee_claimer"}]}}, {"name": "payer", "docs": ["Payer of the partner metadata."], "writable": true, "signer": true}, {"name": "fee_claimer", "docs": ["Fee claimer for partner"], "signer": true}, {"name": "system_program", "docs": ["System program."], "address": "11111111111111111111111111111111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "metadata", "type": {"defined": {"name": "CreatePartnerMetadataParameters"}}}]}, {"name": "create_virtual_pool_metadata", "discriminator": [45, 97, 187, 103, 254, 109, 124, 134], "accounts": [{"name": "virtual_pool", "writable": true}, {"name": "virtual_pool_metadata", "docs": ["Virtual pool metadata"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [118, 105, 114, 116, 117, 97, 108, 95, 112, 111, 111, 108, 95, 109, 101, 116, 97, 100, 97, 116, 97]}, {"kind": "account", "path": "virtual_pool"}]}}, {"name": "creator", "signer": true, "relations": ["virtual_pool"]}, {"name": "payer", "docs": ["Payer of the virtual pool metadata."], "writable": true, "signer": true}, {"name": "system_program", "docs": ["System program."], "address": "11111111111111111111111111111111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "metadata", "type": {"defined": {"name": "CreateVirtualPoolMetadataParameters"}}}]}, {"name": "creator_withdraw_surplus", "discriminator": [165, 3, 137, 7, 28, 134, 76, 80], "accounts": [{"name": "pool_authority", "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "config", "relations": ["virtual_pool"]}, {"name": "virtual_pool", "writable": true}, {"name": "token_quote_account", "docs": ["The receiver token account"], "writable": true}, {"name": "quote_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["virtual_pool"]}, {"name": "quote_mint", "docs": ["The mint of quote token"], "relations": ["config"]}, {"name": "creator", "signer": true, "relations": ["virtual_pool"]}, {"name": "token_quote_program", "docs": ["Token b program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "initialize_virtual_pool_with_spl_token", "docs": ["POOL CREATOR FUNCTIONS ////"], "discriminator": [140, 85, 215, 176, 102, 54, 104, 79], "accounts": [{"name": "config", "docs": ["Which config the pool belongs to."]}, {"name": "pool_authority", "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "creator", "signer": true}, {"name": "base_mint", "writable": true, "signer": true}, {"name": "quote_mint", "relations": ["config"]}, {"name": "pool", "docs": ["Initialize an account to store the pool state"], "writable": true}, {"name": "base_vault", "docs": ["Token a vault for the pool"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "base_mint"}, {"kind": "account", "path": "pool"}]}}, {"name": "quote_vault", "docs": ["To<PERSON> b vault for the pool"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "quote_mint"}, {"kind": "account", "path": "pool"}]}}, {"name": "mint_metadata", "writable": true}, {"name": "metadata_program", "address": "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"}, {"name": "payer", "docs": ["Address paying to create the pool. Can be anyone"], "writable": true, "signer": true}, {"name": "token_quote_program", "docs": ["Program to create mint account and mint tokens"]}, {"name": "token_program", "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "InitializePoolParameters"}}}]}, {"name": "initialize_virtual_pool_with_token2022", "discriminator": [169, 118, 51, 78, 145, 110, 220, 155], "accounts": [{"name": "config", "docs": ["Which config the pool belongs to."]}, {"name": "pool_authority", "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "creator", "signer": true}, {"name": "base_mint", "docs": ["Unique token mint address, initialize in contract"], "writable": true, "signer": true}, {"name": "quote_mint", "relations": ["config"]}, {"name": "pool", "docs": ["Initialize an account to store the pool state"], "writable": true}, {"name": "base_vault", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "base_mint"}, {"kind": "account", "path": "pool"}]}}, {"name": "quote_vault", "docs": ["Token quote vault for the pool"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "quote_mint"}, {"kind": "account", "path": "pool"}]}}, {"name": "payer", "docs": ["Address paying to create the pool. Can be anyone"], "writable": true, "signer": true}, {"name": "token_quote_program", "docs": ["Program to create mint account and mint tokens"]}, {"name": "token_program", "docs": ["token program for base mint"], "address": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "InitializePoolParameters"}}}]}, {"name": "migrate_meteora_damm", "discriminator": [27, 1, 48, 22, 180, 63, 118, 217], "accounts": [{"name": "virtual_pool", "docs": ["virtual pool"], "writable": true, "relations": ["migration_metadata"]}, {"name": "migration_metadata", "writable": true}, {"name": "config", "relations": ["virtual_pool"]}, {"name": "pool_authority", "writable": true, "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "pool", "writable": true}, {"name": "damm_config", "docs": ["pool config"]}, {"name": "lp_mint", "writable": true}, {"name": "token_a_mint", "writable": true}, {"name": "token_b_mint"}, {"name": "a_vault", "writable": true}, {"name": "b_vault", "writable": true}, {"name": "a_token_vault", "writable": true}, {"name": "b_token_vault", "writable": true}, {"name": "a_vault_lp_mint", "writable": true}, {"name": "b_vault_lp_mint", "writable": true}, {"name": "a_vault_lp", "writable": true}, {"name": "b_vault_lp", "writable": true}, {"name": "base_vault", "writable": true, "relations": ["virtual_pool"]}, {"name": "quote_vault", "writable": true, "relations": ["virtual_pool"]}, {"name": "virtual_pool_lp", "writable": true}, {"name": "protocol_token_a_fee", "writable": true}, {"name": "protocol_token_b_fee", "writable": true}, {"name": "payer", "writable": true, "signer": true}, {"name": "rent"}, {"name": "mint_metadata", "writable": true}, {"name": "metadata_program"}, {"name": "amm_program", "address": "Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB"}, {"name": "vault_program"}, {"name": "token_program", "docs": ["token_program"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "associated_token_program"}, {"name": "system_program", "docs": ["System program."], "address": "11111111111111111111111111111111"}], "args": []}, {"name": "migrate_meteora_damm_claim_lp_token", "discriminator": [139, 133, 2, 30, 91, 145, 127, 154], "accounts": [{"name": "virtual_pool", "relations": ["migration_metadata"]}, {"name": "migration_metadata", "docs": ["migration metadata"], "writable": true}, {"name": "pool_authority", "writable": true, "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "lp_mint", "relations": ["migration_metadata"]}, {"name": "source_token", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "pool_authority"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "migration_metadata"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "destination_token", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "owner"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "migration_metadata"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "owner"}, {"name": "sender", "signer": true}, {"name": "token_program", "docs": ["token_program"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}], "args": []}, {"name": "migrate_meteora_damm_lock_lp_token", "discriminator": [177, 55, 238, 157, 251, 88, 165, 42], "accounts": [{"name": "virtual_pool", "relations": ["migration_metadata"]}, {"name": "migration_metadata", "docs": ["migration_metadata"], "writable": true}, {"name": "pool_authority", "writable": true, "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "pool", "writable": true, "relations": ["lock_escrow"]}, {"name": "lp_mint", "relations": ["migration_metadata"]}, {"name": "lock_escrow", "writable": true}, {"name": "owner", "relations": ["lock_escrow"]}, {"name": "source_tokens", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "pool_authority"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "migration_metadata"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "escrow_vault", "writable": true}, {"name": "amm_program", "address": "Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB"}, {"name": "a_vault"}, {"name": "b_vault"}, {"name": "a_vault_lp"}, {"name": "b_vault_lp"}, {"name": "a_vault_lp_mint"}, {"name": "b_vault_lp_mint"}, {"name": "token_program", "docs": ["token_program"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}], "args": []}, {"name": "migration_damm_v2", "discriminator": [156, 169, 230, 103, 53, 228, 80, 64], "accounts": [{"name": "virtual_pool", "docs": ["virtual pool"], "writable": true, "relations": ["migration_metadata"]}, {"name": "migration_metadata", "docs": ["migration metadata"]}, {"name": "config", "docs": ["virtual pool config key"], "relations": ["virtual_pool"]}, {"name": "pool_authority", "writable": true, "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "pool", "writable": true}, {"name": "first_position_nft_mint", "writable": true}, {"name": "first_position_nft_account", "writable": true}, {"name": "first_position", "writable": true}, {"name": "second_position_nft_mint", "writable": true, "optional": true}, {"name": "second_position_nft_account", "writable": true, "optional": true}, {"name": "second_position", "writable": true, "optional": true}, {"name": "damm_pool_authority"}, {"name": "amm_program", "address": "cpamdpZCGKUy5JxQXB4dcpGPiikHawvSWAd6mEn1sGG"}, {"name": "base_mint", "writable": true}, {"name": "quote_mint", "writable": true}, {"name": "token_a_vault", "writable": true}, {"name": "token_b_vault", "writable": true}, {"name": "base_vault", "writable": true, "relations": ["virtual_pool"]}, {"name": "quote_vault", "writable": true, "relations": ["virtual_pool"]}, {"name": "payer", "writable": true, "signer": true}, {"name": "token_base_program"}, {"name": "token_quote_program"}, {"name": "token_2022_program"}, {"name": "damm_event_authority"}, {"name": "system_program", "docs": ["System program."], "address": "11111111111111111111111111111111"}], "args": []}, {"name": "migration_damm_v2_create_metadata", "discriminator": [109, 189, 19, 36, 195, 183, 222, 82], "accounts": [{"name": "virtual_pool"}, {"name": "config", "relations": ["virtual_pool"]}, {"name": "migration_metadata", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [100, 97, 109, 109, 95, 118, 50]}, {"kind": "account", "path": "virtual_pool"}]}}, {"name": "payer", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "migration_meteora_damm_create_metadata", "docs": ["migrate damm v1"], "discriminator": [47, 94, 126, 115, 221, 226, 194, 133], "accounts": [{"name": "virtual_pool"}, {"name": "config", "relations": ["virtual_pool"]}, {"name": "migration_metadata", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [109, 101, 116, 101, 111, 114, 97]}, {"kind": "account", "path": "virtual_pool"}]}}, {"name": "payer", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "partner_withdraw_surplus", "discriminator": [168, 173, 72, 100, 201, 98, 38, 92], "accounts": [{"name": "pool_authority", "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "config", "relations": ["virtual_pool"]}, {"name": "virtual_pool", "writable": true}, {"name": "token_quote_account", "docs": ["The receiver token account"], "writable": true}, {"name": "quote_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["virtual_pool"]}, {"name": "quote_mint", "docs": ["The mint of quote token"], "relations": ["config"]}, {"name": "fee_claimer", "signer": true, "relations": ["config"]}, {"name": "token_quote_program", "docs": ["Token b program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "protocol_withdraw_surplus", "discriminator": [54, 136, 225, 138, 172, 182, 214, 167], "accounts": [{"name": "pool_authority", "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "config", "relations": ["virtual_pool"]}, {"name": "virtual_pool", "writable": true}, {"name": "token_quote_account", "docs": ["The treasury quote token account"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [48, 9, 89, 123, 106, 114, 131, 251, 50, 173, 254, 250, 10, 80, 160, 84, 143, 100, 81, 249, 134, 112, 30, 213, 50, 166, 239, 78, 53, 175, 188, 85]}, {"kind": "account", "path": "token_quote_program"}, {"kind": "account", "path": "quote_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "quote_vault", "docs": ["The vault token account for quote token"], "writable": true, "relations": ["virtual_pool"]}, {"name": "quote_mint", "docs": ["The mint of of token"], "relations": ["config"]}, {"name": "token_quote_program", "docs": ["Token b program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "swap", "docs": ["TRADING BOTS FUNCTIONS ////"], "discriminator": [248, 198, 158, 145, 225, 117, 135, 200], "accounts": [{"name": "pool_authority", "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "config", "docs": ["config key"], "relations": ["pool"]}, {"name": "pool", "docs": ["Pool account"], "writable": true}, {"name": "input_token_account", "docs": ["The user token account for input token"], "writable": true}, {"name": "output_token_account", "docs": ["The user token account for output token"], "writable": true}, {"name": "base_vault", "docs": ["The vault token account for base token"], "writable": true, "relations": ["pool"]}, {"name": "quote_vault", "docs": ["The vault token account for quote token"], "writable": true, "relations": ["pool"]}, {"name": "base_mint", "docs": ["The mint of base token"]}, {"name": "quote_mint", "docs": ["The mint of quote token"]}, {"name": "payer", "docs": ["The user performing the swap"], "signer": true}, {"name": "token_base_program", "docs": ["Token base program"]}, {"name": "token_quote_program", "docs": ["Token quote program"]}, {"name": "referral_token_account", "docs": ["referral token account"], "writable": true, "optional": true}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "SwapParameters"}}}]}, {"name": "transfer_pool_creator", "discriminator": [20, 7, 169, 33, 58, 147, 166, 33], "accounts": [{"name": "virtual_pool", "writable": true}, {"name": "config", "relations": ["virtual_pool"]}, {"name": "creator", "signer": true, "relations": ["virtual_pool"]}, {"name": "new_creator"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "withdraw_leftover", "discriminator": [20, 198, 202, 237, 235, 243, 183, 66], "accounts": [{"name": "pool_authority", "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "config", "relations": ["virtual_pool"]}, {"name": "virtual_pool", "writable": true}, {"name": "token_base_account", "docs": ["The receiver token account, withdraw to ATA"], "writable": true, "pda": {"seeds": [{"kind": "account", "path": "leftover_receiver"}, {"kind": "account", "path": "token_base_program"}, {"kind": "account", "path": "base_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "base_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["virtual_pool"]}, {"name": "base_mint", "docs": ["The mint of quote token"], "relations": ["virtual_pool"]}, {"name": "leftover_receiver", "relations": ["config"]}, {"name": "token_base_program", "docs": ["Token base program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": []}, {"name": "withdraw_migration_fee", "docs": ["BOTH partner and creator FUNCTIONS ///"], "discriminator": [237, 142, 45, 23, 129, 6, 222, 162], "accounts": [{"name": "pool_authority", "address": "FhVo3mqL8PW5pH5U2CN4XE33DokiyZnUwuGpH2hmHLuM"}, {"name": "config", "relations": ["virtual_pool"]}, {"name": "virtual_pool", "writable": true}, {"name": "token_quote_account", "docs": ["The receiver token account"], "writable": true}, {"name": "quote_vault", "docs": ["The vault token account for output token"], "writable": true, "relations": ["virtual_pool"]}, {"name": "quote_mint", "docs": ["The mint of quote token"], "relations": ["config"]}, {"name": "sender", "signer": true}, {"name": "token_quote_program", "docs": ["Token b program"]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "flag", "type": "u8"}]}], "accounts": [{"name": "ClaimFeeOperator", "discriminator": [166, 48, 134, 86, 34, 200, 188, 150]}, {"name": "Config", "discriminator": [155, 12, 170, 224, 30, 250, 204, 130]}, {"name": "LockEscrow", "discriminator": [190, 106, 121, 6, 200, 182, 21, 75]}, {"name": "MeteoraDammMigrationMetadata", "discriminator": [17, 155, 141, 215, 207, 4, 133, 156]}, {"name": "MeteoraDammV2Metadata", "discriminator": [104, 221, 219, 203, 10, 142, 250, 163]}, {"name": "PartnerMetadata", "discriminator": [68, 68, 130, 19, 16, 209, 98, 156]}, {"name": "PoolConfig", "discriminator": [26, 108, 14, 123, 116, 230, 129, 43]}, {"name": "VirtualPool", "discriminator": [213, 224, 5, 209, 98, 69, 119, 92]}, {"name": "VirtualPoolMetadata", "discriminator": [217, 37, 82, 250, 43, 47, 228, 254]}], "events": [{"name": "EvtClaimCreatorTradingFee", "discriminator": [154, 228, 215, 202, 133, 155, 214, 138]}, {"name": "EvtClaimProtocolFee", "discriminator": [186, 244, 75, 251, 188, 13, 25, 33]}, {"name": "EvtClaimTradingFee", "discriminator": [26, 83, 117, 240, 92, 202, 112, 254]}, {"name": "EvtCloseClaimFeeOperator", "discriminator": [111, 39, 37, 55, 110, 216, 194, 23]}, {"name": "EvtCreateClaimFeeOperator", "discriminator": [21, 6, 153, 120, 68, 116, 28, 177]}, {"name": "EvtCreateConfig", "discriminator": [131, 207, 180, 174, 180, 73, 165, 54]}, {"name": "EvtCreateDammV2MigrationMetadata", "discriminator": [103, 111, 132, 168, 140, 253, 150, 114]}, {"name": "EvtCreateMeteoraMigrationMetadata", "discriminator": [99, 167, 133, 63, 214, 143, 175, 139]}, {"name": "EvtCreatorWithdrawSurplus", "discriminator": [152, 73, 21, 15, 66, 87, 53, 157]}, {"name": "EvtCurveComplete", "discriminator": [229, 231, 86, 84, 156, 134, 75, 24]}, {"name": "EvtInitializePool", "discriminator": [228, 50, 246, 85, 203, 66, 134, 37]}, {"name": "EvtPartnerMetadata", "discriminator": [200, 127, 6, 55, 13, 32, 8, 150]}, {"name": "EvtPartnerWithdrawMigrationFee", "discriminator": [181, 105, 127, 67, 8, 187, 120, 57]}, {"name": "EvtPartnerWithdrawSurplus", "discriminator": [195, 56, 152, 9, 232, 72, 35, 22]}, {"name": "EvtProtocolWithdrawSurplus", "discriminator": [109, 111, 28, 221, 134, 195, 230, 203]}, {"name": "EvtSwap", "discriminator": [27, 60, 21, 213, 138, 170, 187, 147]}, {"name": "EvtUpdatePoolCreator", "discriminator": [107, 225, 165, 237, 91, 158, 213, 220]}, {"name": "EvtVirtualPoolMetadata", "discriminator": [188, 18, 72, 76, 195, 91, 38, 74]}, {"name": "EvtWithdrawLeftover", "discriminator": [191, 189, 104, 143, 111, 156, 94, 229]}, {"name": "EvtWithdrawMigrationFee", "discriminator": [26, 203, 84, 85, 161, 23, 100, 214]}], "errors": [{"code": 6000, "name": "MathOverflow", "msg": "Math operation overflow"}, {"code": 6001, "name": "InvalidFee", "msg": "Invalid fee setup"}, {"code": 6002, "name": "ExceededSlippage", "msg": "Exceeded slippage tolerance"}, {"code": 6003, "name": "ExceedMaxFeeBps", "msg": "Exceeded max fee bps"}, {"code": 6004, "name": "InvalidAdmin", "msg": "Invalid admin"}, {"code": 6005, "name": "AmountIsZero", "msg": "Amount is zero"}, {"code": 6006, "name": "TypeCastFailed", "msg": "Type cast error"}, {"code": 6007, "name": "InvalidActivationType", "msg": "Invalid activation type"}, {"code": 6008, "name": "InvalidQuoteMint", "msg": "Invalid quote mint"}, {"code": 6009, "name": "InvalidCollectFeeMode", "msg": "Invalid collect fee mode"}, {"code": 6010, "name": "InvalidMigrationFeeOption", "msg": "Invalid migration fee option"}, {"code": 6011, "name": "InvalidInput", "msg": "Invalid input"}, {"code": 6012, "name": "NotEnoughLiquidity", "msg": "Not enough liquidity"}, {"code": 6013, "name": "PoolIsCompleted", "msg": "Pool is completed"}, {"code": 6014, "name": "PoolIsIncompleted", "msg": "Pool is incompleted"}, {"code": 6015, "name": "InvalidMigrationOption", "msg": "Invalid migration option"}, {"code": 6016, "name": "InvalidTokenDecimals", "msg": "Invalid activation type"}, {"code": 6017, "name": "InvalidTokenType", "msg": "Invalid token type"}, {"code": 6018, "name": "InvalidFeePercentage", "msg": "Invalid fee percentage"}, {"code": 6019, "name": "InvalidQuoteThreshold", "msg": "Invalid quote threshold"}, {"code": 6020, "name": "InvalidTokenSupply", "msg": "Invalid token supply"}, {"code": 6021, "name": "InvalidCurve", "msg": "Invalid curve"}, {"code": 6022, "name": "NotPermitToDoThisAction", "msg": "Not permit to do this action"}, {"code": 6023, "name": "InvalidOwnerAccount", "msg": "Invalid owner account"}, {"code": 6024, "name": "InvalidConfigAccount", "msg": "Invalid config account"}, {"code": 6025, "name": "SurplusHasBeenWithdraw", "msg": "Surplus has been withdraw"}, {"code": 6026, "name": "LeftoverHasBeenWithdraw", "msg": "Leftover has been withdraw"}, {"code": 6027, "name": "TotalBaseTokenExceedMaxSupply", "msg": "Total base token is exceeded max supply"}, {"code": 6028, "name": "UnsupportNativeMintToken2022", "msg": "Unsupport native mint token 2022"}, {"code": 6029, "name": "InsufficientLiquidityForMigration", "msg": "Insufficient liquidity for migration"}, {"code": 6030, "name": "MissingPoolConfigInRemainingAccount", "msg": "Missing pool config in remaining account"}, {"code": 6031, "name": "InvalidVestingParameters", "msg": "Invalid vesting parameters"}, {"code": 6032, "name": "InvalidLeftoverAddress", "msg": "Invalid leftover address"}, {"code": 6033, "name": "SwapAmountIsOverAThreshold", "msg": "Swap amount is over a threshold"}, {"code": 6034, "name": "InvalidFeeScheduler", "msg": "Invalid fee scheduler"}, {"code": 6035, "name": "InvalidCreatorTradingFeePercentage", "msg": "Invalid creator trading fee percentage"}, {"code": 6036, "name": "InvalidNewCreator", "msg": "Invalid new creator"}, {"code": 6037, "name": "InvalidTokenUpdateAuthorityOption", "msg": "Invalid token update authority option"}, {"code": 6038, "name": "Invalid<PERSON><PERSON>unt", "msg": "Invalid account for the instruction"}, {"code": 6039, "name": "InvalidMigratorFeePercentage", "msg": "Invalid migrator fee percentage"}, {"code": 6040, "name": "MigrationFeeHasBeenWithdraw", "msg": "Migration fee has been withdraw"}, {"code": 6041, "name": "InvalidBaseFeeMode", "msg": "Invalid base fee mode"}, {"code": 6042, "name": "InvalidFeeRateLimiter", "msg": "Invalid fee rate limiter"}, {"code": 6043, "name": "FailToValidateSingleSwapInstruction", "msg": "Fail to validate single swap instruction in rate limiter"}], "types": [{"name": "BaseFeeConfig", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "cliff_fee_numerator", "type": "u64"}, {"name": "second_factor", "type": "u64"}, {"name": "third_factor", "type": "u64"}, {"name": "first_factor", "type": "u16"}, {"name": "base_fee_mode", "type": "u8"}, {"name": "padding_0", "type": {"array": ["u8", 5]}}]}}, {"name": "BaseFeeParameters", "type": {"kind": "struct", "fields": [{"name": "cliff_fee_numerator", "type": "u64"}, {"name": "first_factor", "type": "u16"}, {"name": "second_factor", "type": "u64"}, {"name": "third_factor", "type": "u64"}, {"name": "base_fee_mode", "type": "u8"}]}}, {"name": "ClaimFeeOperator", "docs": ["Parameter that set by the protocol"], "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "operator", "docs": ["operator"], "type": "pubkey"}, {"name": "_padding", "docs": ["Reserve"], "type": {"array": ["u8", 128]}}]}}, {"name": "Config", "type": {"kind": "struct", "fields": [{"name": "pool_fees", "type": {"defined": {"name": "PoolFees"}}}, {"name": "activation_duration", "type": "u64"}, {"name": "vault_config_key", "type": "pubkey"}, {"name": "pool_creator_authority", "type": "pubkey"}, {"name": "activation_type", "type": "u8"}, {"name": "partner_fee_numerator", "type": "u64"}, {"name": "padding", "type": {"array": ["u8", 219]}}]}}, {"name": "ConfigParameters", "type": {"kind": "struct", "fields": [{"name": "pool_fees", "type": {"defined": {"name": "PoolFeeParameters"}}}, {"name": "collect_fee_mode", "type": "u8"}, {"name": "migration_option", "type": "u8"}, {"name": "activation_type", "type": "u8"}, {"name": "token_type", "type": "u8"}, {"name": "token_decimal", "type": "u8"}, {"name": "partner_lp_percentage", "type": "u8"}, {"name": "partner_locked_lp_percentage", "type": "u8"}, {"name": "creator_lp_percentage", "type": "u8"}, {"name": "creator_locked_lp_percentage", "type": "u8"}, {"name": "migration_quote_threshold", "type": "u64"}, {"name": "sqrt_start_price", "type": "u128"}, {"name": "locked_vesting", "type": {"defined": {"name": "LockedVestingParams"}}}, {"name": "migration_fee_option", "type": "u8"}, {"name": "token_supply", "type": {"option": {"defined": {"name": "TokenSupplyParams"}}}}, {"name": "creator_trading_fee_percentage", "type": "u8"}, {"name": "token_update_authority", "type": "u8"}, {"name": "migration_fee", "type": {"defined": {"name": "MigrationFee"}}}, {"name": "padding_0", "type": {"array": ["u8", 4]}}, {"name": "padding_1", "docs": ["padding for future use"], "type": {"array": ["u64", 7]}}, {"name": "curve", "type": {"vec": {"defined": {"name": "LiquidityDistributionParameters"}}}}]}}, {"name": "CreatePartnerMetadataParameters", "type": {"kind": "struct", "fields": [{"name": "padding", "type": {"array": ["u8", 96]}}, {"name": "name", "type": "string"}, {"name": "website", "type": "string"}, {"name": "logo", "type": "string"}]}}, {"name": "CreateVirtualPoolMetadataParameters", "type": {"kind": "struct", "fields": [{"name": "padding", "type": {"array": ["u8", 96]}}, {"name": "name", "type": "string"}, {"name": "website", "type": "string"}, {"name": "logo", "type": "string"}]}}, {"name": "DynamicFeeConfig", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "initialized", "type": "u8"}, {"name": "padding", "type": {"array": ["u8", 7]}}, {"name": "max_volatility_accumulator", "type": "u32"}, {"name": "variable_fee_control", "type": "u32"}, {"name": "bin_step", "type": "u16"}, {"name": "filter_period", "type": "u16"}, {"name": "decay_period", "type": "u16"}, {"name": "reduction_factor", "type": "u16"}, {"name": "padding2", "type": {"array": ["u8", 8]}}, {"name": "bin_step_u128", "type": "u128"}]}}, {"name": "DynamicFeeParameters", "type": {"kind": "struct", "fields": [{"name": "bin_step", "type": "u16"}, {"name": "bin_step_u128", "type": "u128"}, {"name": "filter_period", "type": "u16"}, {"name": "decay_period", "type": "u16"}, {"name": "reduction_factor", "type": "u16"}, {"name": "max_volatility_accumulator", "type": "u32"}, {"name": "variable_fee_control", "type": "u32"}]}}, {"name": "EvtClaimCreatorTradingFee", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "token_base_amount", "type": "u64"}, {"name": "token_quote_amount", "type": "u64"}]}}, {"name": "EvtClaimProtocolFee", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "token_base_amount", "type": "u64"}, {"name": "token_quote_amount", "type": "u64"}]}}, {"name": "EvtClaimTradingFee", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "token_base_amount", "type": "u64"}, {"name": "token_quote_amount", "type": "u64"}]}}, {"name": "EvtCloseClaimFeeOperator", "docs": ["Close claim fee operator"], "type": {"kind": "struct", "fields": [{"name": "claim_fee_operator", "type": "pubkey"}, {"name": "operator", "type": "pubkey"}]}}, {"name": "EvtCreateClaimFeeOperator", "docs": ["Create claim fee operator"], "type": {"kind": "struct", "fields": [{"name": "operator", "type": "pubkey"}]}}, {"name": "EvtCreateConfig", "docs": ["Create config"], "type": {"kind": "struct", "fields": [{"name": "config", "type": "pubkey"}, {"name": "quote_mint", "type": "pubkey"}, {"name": "fee_claimer", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}, {"name": "pool_fees", "type": {"defined": {"name": "PoolFeeParameters"}}}, {"name": "collect_fee_mode", "type": "u8"}, {"name": "migration_option", "type": "u8"}, {"name": "activation_type", "type": "u8"}, {"name": "token_decimal", "type": "u8"}, {"name": "token_type", "type": "u8"}, {"name": "partner_locked_lp_percentage", "type": "u8"}, {"name": "partner_lp_percentage", "type": "u8"}, {"name": "creator_locked_lp_percentage", "type": "u8"}, {"name": "creator_lp_percentage", "type": "u8"}, {"name": "swap_base_amount", "type": "u64"}, {"name": "migration_quote_threshold", "type": "u64"}, {"name": "migration_base_amount", "type": "u64"}, {"name": "sqrt_start_price", "type": "u128"}, {"name": "locked_vesting", "type": {"defined": {"name": "LockedVestingParams"}}}, {"name": "migration_fee_option", "type": "u8"}, {"name": "fixed_token_supply_flag", "type": "u8"}, {"name": "pre_migration_token_supply", "type": "u64"}, {"name": "post_migration_token_supply", "type": "u64"}, {"name": "curve", "type": {"vec": {"defined": {"name": "LiquidityDistributionParameters"}}}}]}}, {"name": "EvtCreateDammV2MigrationMetadata", "type": {"kind": "struct", "fields": [{"name": "virtual_pool", "type": "pubkey"}]}}, {"name": "EvtCreateMeteoraMigrationMetadata", "type": {"kind": "struct", "fields": [{"name": "virtual_pool", "type": "pubkey"}]}}, {"name": "EvtCreatorWithdrawSurplus", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "surplus_amount", "type": "u64"}]}}, {"name": "EvtCurveComplete", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "config", "type": "pubkey"}, {"name": "base_reserve", "type": "u64"}, {"name": "quote_reserve", "type": "u64"}]}}, {"name": "EvtInitializePool", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "config", "type": "pubkey"}, {"name": "creator", "type": "pubkey"}, {"name": "base_mint", "type": "pubkey"}, {"name": "pool_type", "type": "u8"}, {"name": "activation_point", "type": "u64"}]}}, {"name": "EvtPartnerMetadata", "docs": ["Create partner metadata"], "type": {"kind": "struct", "fields": [{"name": "partner_metadata", "type": "pubkey"}, {"name": "fee_claimer", "type": "pubkey"}]}}, {"name": "EvtPartnerWithdrawMigrationFee", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "fee", "type": "u64"}]}}, {"name": "EvtPartnerWithdrawSurplus", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "surplus_amount", "type": "u64"}]}}, {"name": "EvtProtocolWithdrawSurplus", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "surplus_amount", "type": "u64"}]}}, {"name": "EvtSwap", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "config", "type": "pubkey"}, {"name": "trade_direction", "type": "u8"}, {"name": "has_referral", "type": "bool"}, {"name": "params", "type": {"defined": {"name": "SwapParameters"}}}, {"name": "swap_result", "type": {"defined": {"name": "SwapResult"}}}, {"name": "amount_in", "type": "u64"}, {"name": "current_timestamp", "type": "u64"}]}}, {"name": "EvtUpdatePoolCreator", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "creator", "type": "pubkey"}, {"name": "new_creator", "type": "pubkey"}]}}, {"name": "EvtVirtualPoolMetadata", "docs": ["Create virtual pool metadata"], "type": {"kind": "struct", "fields": [{"name": "virtual_pool_metadata", "type": "pubkey"}, {"name": "virtual_pool", "type": "pubkey"}]}}, {"name": "EvtWithdrawLeftover", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "leftover_receiver", "type": "pubkey"}, {"name": "leftover_amount", "type": "u64"}]}}, {"name": "EvtWithdrawMigrationFee", "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "fee", "type": "u64"}, {"name": "flag", "type": "u8"}]}}, {"name": "InitializePoolParameters", "type": {"kind": "struct", "fields": [{"name": "name", "type": "string"}, {"name": "symbol", "type": "string"}, {"name": "uri", "type": "string"}]}}, {"name": "LiquidityDistributionConfig", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "sqrt_price", "type": "u128"}, {"name": "liquidity", "type": "u128"}]}}, {"name": "LiquidityDistributionParameters", "type": {"kind": "struct", "fields": [{"name": "sqrt_price", "type": "u128"}, {"name": "liquidity", "type": "u128"}]}}, {"name": "LockEscrow", "docs": ["State of lock escrow account"], "type": {"kind": "struct", "fields": [{"name": "pool", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}, {"name": "escrow_vault", "type": "pubkey"}, {"name": "bump", "type": "u8"}, {"name": "total_locked_amount", "type": "u64"}, {"name": "lp_per_token", "type": "u128"}, {"name": "unclaimed_fee_pending", "type": "u64"}, {"name": "a_fee", "type": "u64"}, {"name": "b_fee", "type": "u64"}]}}, {"name": "LockedVestingConfig", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "amount_per_period", "type": "u64"}, {"name": "cliff_duration_from_migration_time", "type": "u64"}, {"name": "frequency", "type": "u64"}, {"name": "number_of_period", "type": "u64"}, {"name": "cliff_unlock_amount", "type": "u64"}, {"name": "_padding", "type": "u64"}]}}, {"name": "LockedVestingParams", "type": {"kind": "struct", "fields": [{"name": "amount_per_period", "type": "u64"}, {"name": "cliff_duration_from_migration_time", "type": "u64"}, {"name": "frequency", "type": "u64"}, {"name": "number_of_period", "type": "u64"}, {"name": "cliff_unlock_amount", "type": "u64"}]}}, {"name": "MeteoraDammMigrationMetadata", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "virtual_pool", "docs": ["pool"], "type": "pubkey"}, {"name": "padding_0", "docs": ["!!! BE CAREFUL to use tomestone field, previous is pool creator"], "type": {"array": ["u8", 32]}}, {"name": "partner", "docs": ["partner"], "type": "pubkey"}, {"name": "lp_mint", "docs": ["lp mint"], "type": "pubkey"}, {"name": "partner_locked_lp", "docs": ["partner locked lp"], "type": "u64"}, {"name": "partner_lp", "docs": ["partner lp"], "type": "u64"}, {"name": "creator_locked_lp", "docs": ["creator locked lp"], "type": "u64"}, {"name": "creator_lp", "docs": ["creator lp"], "type": "u64"}, {"name": "_padding_0", "docs": ["padding"], "type": "u8"}, {"name": "creator_locked_status", "docs": ["flag to check whether lp is locked for creator"], "type": "u8"}, {"name": "partner_locked_status", "docs": ["flag to check whether lp is locked for partner"], "type": "u8"}, {"name": "creator_claim_status", "docs": ["flag to check whether creator has claimed lp token"], "type": "u8"}, {"name": "partner_claim_status", "docs": ["flag to check whether partner has claimed lp token"], "type": "u8"}, {"name": "_padding", "docs": ["Reserve"], "type": {"array": ["u8", 107]}}]}}, {"name": "MeteoraDammV2Metadata", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "virtual_pool", "docs": ["pool"], "type": "pubkey"}, {"name": "padding_0", "docs": ["!!! BE CAREFUL to use tomestone field, previous is pool creator"], "type": {"array": ["u8", 32]}}, {"name": "partner", "docs": ["partner"], "type": "pubkey"}, {"name": "_padding", "docs": ["Reserve"], "type": {"array": ["u8", 126]}}]}}, {"name": "MigrationFee", "type": {"kind": "struct", "fields": [{"name": "fee_percentage", "type": "u8"}, {"name": "creator_fee_percentage", "type": "u8"}]}}, {"name": "PartnerMetadata", "docs": ["<PERSON><PERSON><PERSON> for a partner."], "type": {"kind": "struct", "fields": [{"name": "fee_claimer", "docs": ["fee claimer"], "type": "pubkey"}, {"name": "padding", "docs": ["padding for future use"], "type": {"array": ["u128", 6]}}, {"name": "name", "docs": ["Name of partner."], "type": "string"}, {"name": "website", "docs": ["Website of partner."], "type": "string"}, {"name": "logo", "docs": ["Logo of partner"], "type": "string"}]}}, {"name": "PoolConfig", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "quote_mint", "docs": ["quote mint"], "type": "pubkey"}, {"name": "fee_claimer", "docs": ["Address to get the fee"], "type": "pubkey"}, {"name": "leftover_receiver", "docs": ["Address to receive extra base token after migration, in case token is fixed supply"], "type": "pubkey"}, {"name": "pool_fees", "docs": ["Pool fee"], "type": {"defined": {"name": "PoolFeesConfig"}}}, {"name": "collect_fee_mode", "docs": ["Collect fee mode"], "type": "u8"}, {"name": "migration_option", "docs": ["migration option"], "type": "u8"}, {"name": "activation_type", "docs": ["whether mode slot or timestamp"], "type": "u8"}, {"name": "token_decimal", "docs": ["token decimals"], "type": "u8"}, {"name": "version", "docs": ["version"], "type": "u8"}, {"name": "token_type", "docs": ["token type of base token"], "type": "u8"}, {"name": "quote_token_flag", "docs": ["quote token flag"], "type": "u8"}, {"name": "partner_locked_lp_percentage", "docs": ["partner locked lp percentage"], "type": "u8"}, {"name": "partner_lp_percentage", "docs": ["partner lp percentage"], "type": "u8"}, {"name": "creator_locked_lp_percentage", "docs": ["creator post migration fee percentage"], "type": "u8"}, {"name": "creator_lp_percentage", "docs": ["creator lp percentage"], "type": "u8"}, {"name": "migration_fee_option", "docs": ["migration fee option"], "type": "u8"}, {"name": "fixed_token_supply_flag", "docs": ["flag to indicate whether token is dynamic supply (0) or fixed supply (1)"], "type": "u8"}, {"name": "creator_trading_fee_percentage", "docs": ["creator trading fee percentage"], "type": "u8"}, {"name": "token_update_authority", "docs": ["token update authority"], "type": "u8"}, {"name": "migration_fee_percentage", "docs": ["migration fee percentage"], "type": "u8"}, {"name": "creator_migration_fee_percentage", "docs": ["creator migration fee percentage"], "type": "u8"}, {"name": "_padding_1", "docs": ["padding 1"], "type": {"array": ["u8", 7]}}, {"name": "swap_base_amount", "docs": ["swap base amount"], "type": "u64"}, {"name": "migration_quote_threshold", "docs": ["migration quote threshold (in quote token)"], "type": "u64"}, {"name": "migration_base_threshold", "docs": ["migration base threshold (in base token)"], "type": "u64"}, {"name": "migration_sqrt_price", "docs": ["migration sqrt price"], "type": "u128"}, {"name": "locked_vesting_config", "docs": ["locked vesting config"], "type": {"defined": {"name": "LockedVestingConfig"}}}, {"name": "pre_migration_token_supply", "docs": ["pre migration token supply"], "type": "u64"}, {"name": "post_migration_token_supply", "docs": ["post migration token supply"], "type": "u64"}, {"name": "_padding_2", "docs": ["padding 2"], "type": {"array": ["u128", 2]}}, {"name": "sqrt_start_price", "docs": ["minimum price"], "type": "u128"}, {"name": "curve", "docs": ["curve, only use 20 point firstly, we can extend that latter"], "type": {"array": [{"defined": {"name": "LiquidityDistributionConfig"}}, 20]}}]}}, {"name": "PoolFeeParameters", "docs": ["Information regarding fee charges"], "type": {"kind": "struct", "fields": [{"name": "base_fee", "docs": ["Base fee"], "type": {"defined": {"name": "BaseFeeParameters"}}}, {"name": "dynamic_fee", "docs": ["dynamic fee"], "type": {"option": {"defined": {"name": "DynamicFeeParameters"}}}}]}}, {"name": "PoolFees", "docs": ["Information regarding fee charges"], "type": {"kind": "struct", "fields": [{"name": "trade_fee_numerator", "type": "u64"}, {"name": "trade_fee_denominator", "type": "u64"}, {"name": "protocol_trade_fee_numerator", "type": "u64"}, {"name": "protocol_trade_fee_denominator", "type": "u64"}]}}, {"name": "PoolFeesConfig", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "base_fee", "type": {"defined": {"name": "BaseFeeConfig"}}}, {"name": "dynamic_fee", "type": {"defined": {"name": "DynamicFeeConfig"}}}, {"name": "padding_0", "type": {"array": ["u64", 5]}}, {"name": "padding_1", "type": {"array": ["u8", 6]}}, {"name": "protocol_fee_percent", "type": "u8"}, {"name": "referral_fee_percent", "type": "u8"}]}}, {"name": "PoolMetrics", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "total_protocol_base_fee", "type": "u64"}, {"name": "total_protocol_quote_fee", "type": "u64"}, {"name": "total_trading_base_fee", "type": "u64"}, {"name": "total_trading_quote_fee", "type": "u64"}]}}, {"name": "SwapParameters", "type": {"kind": "struct", "fields": [{"name": "amount_in", "type": "u64"}, {"name": "minimum_amount_out", "type": "u64"}]}}, {"name": "SwapResult", "docs": ["Encodes all results of swapping"], "type": {"kind": "struct", "fields": [{"name": "actual_input_amount", "type": "u64"}, {"name": "output_amount", "type": "u64"}, {"name": "next_sqrt_price", "type": "u128"}, {"name": "trading_fee", "type": "u64"}, {"name": "protocol_fee", "type": "u64"}, {"name": "referral_fee", "type": "u64"}]}}, {"name": "TokenSupplyParams", "type": {"kind": "struct", "fields": [{"name": "pre_migration_token_supply", "docs": ["pre migration token supply"], "type": "u64"}, {"name": "post_migration_token_supply", "docs": ["post migration token supply", "becase DBC allow user to swap over the migration quote threshold, so in extreme case user may swap more than allowed buffer on curve", "that result the total supply in post migration may be increased a bit (between pre_migration_token_supply and post_migration_token_supply)"], "type": "u64"}]}}, {"name": "VirtualPool", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "volatility_tracker", "docs": ["volatility tracker"], "type": {"defined": {"name": "VolatilityTracker"}}}, {"name": "config", "docs": ["config key"], "type": "pubkey"}, {"name": "creator", "docs": ["creator"], "type": "pubkey"}, {"name": "base_mint", "docs": ["base mint"], "type": "pubkey"}, {"name": "base_vault", "docs": ["base vault"], "type": "pubkey"}, {"name": "quote_vault", "docs": ["quote vault"], "type": "pubkey"}, {"name": "base_reserve", "docs": ["base reserve"], "type": "u64"}, {"name": "quote_reserve", "docs": ["quote reserve"], "type": "u64"}, {"name": "protocol_base_fee", "docs": ["protocol base fee"], "type": "u64"}, {"name": "protocol_quote_fee", "docs": ["protocol quote fee"], "type": "u64"}, {"name": "partner_base_fee", "docs": ["partner base fee"], "type": "u64"}, {"name": "partner_quote_fee", "docs": ["trading quote fee"], "type": "u64"}, {"name": "sqrt_price", "docs": ["current price"], "type": "u128"}, {"name": "activation_point", "docs": ["Activation point"], "type": "u64"}, {"name": "pool_type", "docs": ["pool type, spl token or token2022"], "type": "u8"}, {"name": "is_migrated", "docs": ["is migrated"], "type": "u8"}, {"name": "is_partner_withdraw_surplus", "docs": ["is partner withdraw surplus"], "type": "u8"}, {"name": "is_protocol_withdraw_surplus", "docs": ["is protocol withdraw surplus"], "type": "u8"}, {"name": "migration_progress", "docs": ["migration progress"], "type": "u8"}, {"name": "is_withdraw_leftover", "docs": ["is withdraw leftover"], "type": "u8"}, {"name": "is_creator_withdraw_surplus", "docs": ["is creator withdraw surplus"], "type": "u8"}, {"name": "migration_fee_withdraw_status", "docs": ["migration fee withdraw status, first bit is for partner, second bit is for creator"], "type": "u8"}, {"name": "metrics", "docs": ["pool metrics"], "type": {"defined": {"name": "PoolMetrics"}}}, {"name": "finish_curve_timestamp", "docs": ["The time curve is finished"], "type": "u64"}, {"name": "creator_base_fee", "docs": ["creator base fee"], "type": "u64"}, {"name": "creator_quote_fee", "docs": ["creator quote fee"], "type": "u64"}, {"name": "_padding_1", "docs": ["Padding for further use"], "type": {"array": ["u64", 7]}}]}}, {"name": "VirtualPoolMetadata", "docs": ["Metadata for a virtual pool."], "type": {"kind": "struct", "fields": [{"name": "virtual_pool", "docs": ["virtual pool"], "type": "pubkey"}, {"name": "padding", "docs": ["padding for future use"], "type": {"array": ["u128", 6]}}, {"name": "name", "docs": ["Name of project."], "type": "string"}, {"name": "website", "docs": ["Website of project."], "type": "string"}, {"name": "logo", "docs": ["Logo of project"], "type": "string"}]}}, {"name": "VolatilityTracker", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "last_update_timestamp", "type": "u64"}, {"name": "padding", "type": {"array": ["u8", 8]}}, {"name": "sqrt_price_reference", "type": "u128"}, {"name": "volatility_accumulator", "type": "u128"}, {"name": "volatility_reference", "type": "u128"}]}}]}