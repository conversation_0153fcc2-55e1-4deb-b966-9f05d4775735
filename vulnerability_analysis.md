# Slippage Protection Vulnerability Analysis

## Overview
This document provides a detailed analysis of the slippage protection vulnerabilities identified in `virtual_pool.rs`.

## Vulnerability 1: BUY Operations - Unscaled Token Comparison

### Current Implementation
In `swap_exact_in.rs` line 33:
```rust
require!(
    swap_result.output_amount >= minimum_amount_out,
    PoolError::ExceededSlippage
);
```

### The Problem
- `swap_result.output_amount` may be unscaled (raw token units)
- `minimum_amount_out` is expected to be scaled (with decimals)
- Comparison fails to account for token decimal scaling

### Exploit Scenario
1. User expects 1000 tokens (1000 * 10^6 = 1,000,000,000 scaled units)
2. Pool returns 1000 unscaled units
3. Current check: `1000 >= 1,000,000,000` → FAILS (good)
4. But if user mistakenly passes unscaled minimum: `1000 >= 1000` → PASSES (bad)
5. User receives 0.001 tokens instead of 1000 tokens

### Code Location
- File: `programs/dynamic-bonding-curve/src/instructions/swap/swap_exact_in.rs`
- Line: 33
- Function: `process_swap_exact_in`

## Vulnerability 2: SELL Operations - Gross vs Net Amount Check

### Current Implementation  
In `swap_exact_out.rs` line 30:
```rust
require!(
    included_fee_input_amount <= maximum_amount_in,
    PoolError::ExceededSlippage
);
```

### The Problem
- `included_fee_input_amount` includes fees (gross amount)
- `maximum_amount_in` is user's expectation for net amount they pay
- User pays more than expected due to fees not being properly accounted

### Exploit Scenario
1. User wants to sell tokens for minimum 0.9 SOL net
2. Pool calculates: 0.9 SOL + 0.1 SOL fees = 1.0 SOL gross
3. Current check: `1.0 SOL <= 0.95 SOL` → FAILS (should fail)
4. But if fees are higher: `1.2 SOL <= 0.95 SOL` → FAILS (good)
5. However, if user passes gross amount: `1.2 SOL <= 1.2 SOL` → PASSES
6. User pays 1.2 SOL but only gets 0.8 SOL net (after 0.4 SOL fees)

### Code Location
- File: `programs/dynamic-bonding-curve/src/instructions/swap/swap_exact_out.rs`  
- Line: 30
- Function: `process_swap_exact_out`

## Impact Assessment

### Financial Impact
- **Direct Loss**: Users receive fewer tokens or pay more SOL than expected
- **Slippage Bypass**: Trades execute when they should be rejected
- **MEV Exploitation**: Attackers can exploit predictable slippage failures

### Affected Functions
1. `process_swap_exact_in` - BUY operations
2. `process_swap_exact_out` - SELL operations  
3. `process_swap_partial_fill` - Partial fill operations

## Recommended Fixes

### Fix 1: BUY Operations
```rust
// Compare scaled, after-fee amount
let out_unscaled = swap_result.output_amount;
let out_scaled = out_unscaled.checked_mul(10u64.pow(DECIMALS))
    .ok_or(PoolError::Overflow)?;
require!(out_scaled >= minimum_amount_out, PoolError::ExceededSlippage);
```

### Fix 2: SELL Operations
```rust  
// Compare net after fees
let sol_gross = swap_result.included_fee_input_amount;
let total_fees = swap_result.trading_fee + swap_result.protocol_fee + swap_result.referral_fee;
let sol_net = sol_gross.checked_sub(total_fees)
    .ok_or(PoolError::Math)?;
require!(sol_net >= minimum_sol_output, PoolError::ExceededSlippage);
```

## Testing Strategy

### Unit Tests Needed
1. Test BUY with scaled vs unscaled amounts
2. Test SELL with gross vs net amounts  
3. Test edge cases with high fees
4. Test with different decimal configurations

### Integration Tests Needed
1. End-to-end swap scenarios
2. MEV attack simulations
3. Slippage boundary testing
4. Fee calculation validation

## Risk Classification
- **Severity**: HIGH
- **Likelihood**: MEDIUM  
- **Impact**: HIGH
- **Overall Risk**: HIGH

## References
- Issue file: `programs/dynamic-bonding-curve/issue.md`
- Primer pattern: "Inaccurate calculation results / loss of precision"
- Related files: `virtual_pool.rs`, swap instruction handlers
