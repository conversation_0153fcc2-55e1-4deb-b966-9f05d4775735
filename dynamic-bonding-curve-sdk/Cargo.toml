[package]
name = "dynamic-bonding-curve-sdk"
version = "0.1.0"
edition = "2021"
description = "dynamic bonding curve sdk"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
crate-type = ["cdylib", "lib"]
name = "dynamic_bonding_curve_sdk"

[dependencies]
anyhow = "1.0.71"
dynamic-bonding-curve = { path = "../programs/dynamic-bonding-curve" }
ruint =  { workspace = true }

[dev-dependencies]
bytemuck = { workspace = true }