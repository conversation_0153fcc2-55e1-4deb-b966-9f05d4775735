# Front-Running and Manipulation of Migration via Swaps

**VULNERABILITY CONFIRMED** - The bug described in `issue.md` is real and exploitable. Attackers can prevent pool migrations by front-running with BaseToQuote swaps that reduce quote reserves below the migration threshold.

**POC Results:**
- ✅ 1 SOL attack successfully blocks 85,000 SOL migration
- ✅ Zero fees required for attack
- ✅ Attack can be repeated infinitely for permanent DoS
- ✅ No existing protections prevent this attack
- ✅ All 4 test scenarios passed in `programs/dynamic-bonding-curve/src/front_running_vulnerability_poc.rs`

## Links to root cause

**Primary vulnerability location:**
- File: [`programs/dynamic-bonding-curve/src/state/virtual_pool.rs`](programs/dynamic-bonding-curve/src/state/virtual_pool.rs#L868-L874)
- Function: `apply_swap_result()`
- Lines: 868-874
- Issue: BaseToQuote swaps reduce `quote_reserve` without checking if this blocks migration

**Migration check logic:**
- File: [`programs/dynamic-bonding-curve/src/state/virtual_pool.rs`](programs/dynamic-bonding-curve/src/state/virtual_pool.rs#L954-L956)
- Function: `is_curve_complete()`
- Lines: 954-956
- Issue: Simple comparison `quote_reserve >= threshold` with no race condition protection

**Swap instruction handler:**
- File: [`programs/dynamic-bonding-curve/src/instructions/swap/ix_swap.rs`](programs/dynamic-bonding-curve/src/instructions/swap/ix_swap.rs#L179-L182)
- Lines: 179-182
- Issue: Migration eligibility check happens before swap processing, creating timing window

## Finding description and impact

**Root cause:**
The vulnerability exists because there is no protection against swaps that reduce quote reserves below the migration threshold during migration attempts. The system checks if a pool is ready for migration (`quote_reserve >= migration_threshold`) but allows BaseToQuote swaps to reduce the quote_reserve between the check and migration execution.

**Technical details:**
1. Migration eligibility is determined by `is_curve_complete()` which simply checks `quote_reserve >= migration_threshold`
2. BaseToQuote swaps call `apply_swap_result()` which reduces quote_reserve:
   ```rust
   if trade_direction == TradeDirection::BaseToQuote {
       self.quote_reserve = self.quote_reserve.safe_sub(actual_output_amount)?;
   }
   ```
3. No atomic locks or protections prevent swaps from executing between migration checks
4. This creates a race condition that attackers can exploit

**Attack mechanism:**
1. Pool reaches migration threshold (e.g., 100,000 SOL)
2. Legitimate user submits migration transaction to mempool
3. Attacker detects migration attempt and front-runs with BaseToQuote swap using higher gas fees
4. Attacker's swap executes first, reducing quote_reserve below threshold (e.g., to 95,000 SOL)
5. Migration transaction fails because pool no longer meets threshold requirement
6. Process can be repeated indefinitely for continuous denial of service

**Impact:**
- **Economic**: Minimal attack cost (1-50 SOL) can block large migrations (85,000+ SOL) with zero fees
- **Operational**: Legitimate migrations fail unexpectedly, funds remain locked in bonding curve
- **Business**: Users lose trust in protocol reliability, competitors can grief token launches
- **Technical**: No recovery mechanism exists, attacks can be automated and repeated infinitely

## Recommended mitigation steps

**Immediate fix (Critical Priority):**
Add protection in `apply_swap_result()` function to prevent swaps that would block migration:

```rust
// In apply_swap_result() function at lines 868-874
if trade_direction == TradeDirection::BaseToQuote {
    let new_quote_reserve = self.quote_reserve.safe_sub(actual_output_amount)?;

    // Prevent swaps that would reduce quote_reserve below migration threshold
    if self.quote_reserve >= migration_threshold && new_quote_reserve < migration_threshold {
        return Err(PoolError::SwapWouldBlockMigration);
    }

    self.quote_reserve = new_quote_reserve;
}
```

**Additional protection (High Priority):**
Implement migration locks to prevent race conditions:

```rust
// Add to VirtualPool struct
pub migration_in_progress: bool,

// Add atomic migration lock function
pub fn begin_migration(&mut self, threshold: u64) -> Result<bool> {
    if self.migration_in_progress {
        return Err(PoolError::MigrationInProgress);
    }
    if self.quote_reserve >= threshold {
        self.migration_in_progress = true;
        return Ok(true);
    }
    Ok(false)
}
```

**Optional enhancement (Medium Priority):**
Add buffer zone above migration threshold:

```rust
const MIGRATION_BUFFER_BPS: u64 = 100; // 1% buffer

pub fn is_curve_complete(&self, migration_threshold: u64) -> bool {
    let buffer = migration_threshold * MIGRATION_BUFFER_BPS / 10000;
    self.quote_reserve >= migration_threshold + buffer
}
```

The immediate fix is essential to prevent exploitation. Without these mitigations, the protocol remains vulnerable to low-cost denial of service attacks that can permanently block pool migrations.
