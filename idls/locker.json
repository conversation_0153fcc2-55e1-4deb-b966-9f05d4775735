{"address": "LocpQgucEQHbqNABEYvBvwoxCPsSbG91A1QaQhQQqjn", "metadata": {"name": "locker", "version": "0.4.0", "spec": "0.1.0", "description": "Created with <PERSON><PERSON>"}, "instructions": [{"name": "cancel_vesting_escrow", "docs": ["Cancel a vesting escrow.", "- The claimable token will be transferred to recipient", "- The remaining token will be transferred to the creator", "This instruction supports both splToken and token2022", "# Arguments", "", "* ctx - The accounts needed by instruction.", "* remaining_accounts_info: additional accounts needed by instruction", ""], "discriminator": [217, 233, 13, 3, 143, 101, 53, 201], "accounts": [{"name": "escrow", "docs": ["Escrow."], "writable": true}, {"name": "token_mint", "docs": ["Mint."], "writable": true, "relations": ["escrow"]}, {"name": "escrow_token", "docs": ["<PERSON><PERSON><PERSON> Account."], "writable": true, "pda": {"seeds": [{"kind": "account", "path": "escrow"}, {"kind": "account", "path": "token_program"}, {"kind": "account", "path": "token_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "creator_token", "docs": ["Creator To<PERSON> Account."], "writable": true, "pda": {"seeds": [{"kind": "account", "path": "escrow"}, {"kind": "account", "path": "token_program"}, {"kind": "account", "path": "token_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "recipient_token", "docs": ["Re<PERSON><PERSON><PERSON> Account."], "writable": true, "pda": {"seeds": [{"kind": "account", "path": "escrow"}, {"kind": "account", "path": "token_program"}, {"kind": "account", "path": "token_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "rent_receiver", "docs": ["CHECKED: The Token Account will receive the rent"], "writable": true}, {"name": "signer", "docs": ["Signer."], "writable": true, "signer": true}, {"name": "memo_program", "docs": ["Memo program."], "address": "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"}, {"name": "token_program", "docs": ["Token program."]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "remaining_accounts_info", "type": {"option": {"defined": {"name": "RemainingAccountsInfo"}}}}]}, {"name": "claim", "docs": ["Claim maximum amount from the vesting escrow", "# Arguments", "", "* ctx - The accounts needed by instruction.", "* max_amount - The maximum amount claimed by the recipient", ""], "discriminator": [62, 198, 214, 193, 213, 159, 108, 210], "accounts": [{"name": "escrow", "docs": ["Escrow."], "writable": true}, {"name": "escrow_token", "docs": ["<PERSON><PERSON><PERSON> Account."], "writable": true, "pda": {"seeds": [{"kind": "account", "path": "escrow"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "escrow"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "recipient", "docs": ["Recipient."], "writable": true, "signer": true, "relations": ["escrow"]}, {"name": "recipient_token", "docs": ["Recipient Token Account."], "writable": true}, {"name": "token_program", "docs": ["Token program."], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "max_amount", "type": "u64"}]}, {"name": "claim_v2", "docs": ["Claim maximum amount from the vesting escrow", "This instruction supports both splToken and token2022", "# Arguments", "", "* ctx - The accounts needed by instruction.", "* max_amount - The maximum amount claimed by the recipient", "* remaining_accounts_info: additional accounts needed by instruction", ""], "discriminator": [229, 87, 46, 162, 21, 157, 231, 114], "accounts": [{"name": "escrow", "docs": ["Escrow."], "writable": true}, {"name": "token_mint", "docs": ["Mint."], "relations": ["escrow"]}, {"name": "escrow_token", "docs": ["<PERSON><PERSON><PERSON> Account."], "writable": true, "pda": {"seeds": [{"kind": "account", "path": "escrow"}, {"kind": "account", "path": "token_program"}, {"kind": "account", "path": "token_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "recipient", "docs": ["Recipient."], "writable": true, "signer": true, "relations": ["escrow"]}, {"name": "recipient_token", "docs": ["Recipient Token Account."], "writable": true}, {"name": "memo_program", "docs": ["Memo program."], "address": "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"}, {"name": "token_program", "docs": ["Token program."]}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "max_amount", "type": "u64"}, {"name": "remaining_accounts_info", "type": {"option": {"defined": {"name": "RemainingAccountsInfo"}}}}]}, {"name": "close_vesting_escrow", "docs": ["Close vesting escrow", "- Close vesting escrow and escrow ATA and escrow metadata if recipient already claimed all tokens", "- Rent receiver must be escrow's creator", "This instruction supports both splToken and token2022", "# Arguments", "", "* ctx - The accounts needed by instruction.", "* remaining_accounts_info: additional accounts needed by instruction", ""], "discriminator": [221, 185, 95, 135, 136, 67, 252, 87], "accounts": [{"name": "escrow", "docs": ["Escrow."], "writable": true}, {"name": "escrow_metadata", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [101, 115, 99, 114, 111, 119, 95, 109, 101, 116, 97, 100, 97, 116, 97]}, {"kind": "account", "path": "escrow"}]}}, {"name": "token_mint", "docs": ["Mint."], "writable": true}, {"name": "escrow_token", "writable": true}, {"name": "creator_token", "writable": true}, {"name": "creator", "docs": ["Creator."], "writable": true, "signer": true, "relations": ["escrow"]}, {"name": "token_program", "docs": ["Token program."]}, {"name": "memo_program", "docs": ["Memo program."], "address": "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "remaining_accounts_info", "type": {"option": {"defined": {"name": "RemainingAccountsInfo"}}}}]}, {"name": "create_root_escrow", "docs": ["Create root escrow"], "discriminator": [116, 212, 12, 188, 77, 226, 32, 201], "accounts": [{"name": "base", "signer": true}, {"name": "root_escrow", "docs": ["Root Escrow."], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [114, 111, 111, 116, 95, 101, 115, 99, 114, 111, 119]}, {"kind": "account", "path": "base"}, {"kind": "account", "path": "token_mint"}, {"kind": "arg", "path": "params.version"}]}}, {"name": "token_mint"}, {"name": "payer", "docs": ["payer."], "writable": true, "signer": true}, {"name": "creator"}, {"name": "system_program", "docs": ["system program."], "address": "******************************11"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "CreateRootEscrowParameters"}}}]}, {"name": "create_vesting_escrow", "docs": ["Create a vesting escrow for the given params", "# Arguments", "", "* ctx - The accounts needed by instruction.", "* params - The params needed by instruction.", "* vesting_start_time - The creation time of this escrow", "* cliff_time - Trade cliff time of the escrow", "* frequency - How frequent the claimable amount will be updated", "* cliff_unlock_amount - The amount unlocked after cliff time", "* amount_per_period - The amount unlocked per vesting period", "* number_of_period - The total number of vesting period", "* update_recipient_mode - <PERSON>ide who can update the recipient of the escrow", "* cancel_mode - Decide who can cancel the the escrow", ""], "discriminator": [23, 100, 197, 94, 222, 153, 38, 90], "accounts": [{"name": "base", "docs": ["Base."], "writable": true, "signer": true}, {"name": "escrow", "docs": ["Escrow."], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [101, 115, 99, 114, 111, 119]}, {"kind": "account", "path": "base"}]}}, {"name": "escrow_token", "docs": ["<PERSON><PERSON><PERSON> Account."], "writable": true, "pda": {"seeds": [{"kind": "account", "path": "escrow"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "sender_token.mint", "account": "<PERSON><PERSON><PERSON><PERSON>unt"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "sender", "docs": ["Sender."], "writable": true, "signer": true}, {"name": "sender_token", "docs": ["Sender Token Account."], "writable": true}, {"name": "recipient"}, {"name": "token_program", "docs": ["Token program."], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "system_program", "docs": ["system program."], "address": "******************************11"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "CreateVestingEscrowParameters"}}}]}, {"name": "create_vesting_escrow_from_root", "docs": ["Crate vesting escrow from root"], "discriminator": [6, 238, 161, 108, 252, 114, 246, 91], "accounts": [{"name": "root_escrow", "docs": ["Root Escrow."], "writable": true}, {"name": "base", "pda": {"seeds": [{"kind": "const", "value": [98, 97, 115, 101]}, {"kind": "account", "path": "root_escrow"}, {"kind": "account", "path": "recipient"}]}}, {"name": "escrow", "docs": ["Escrow."], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [101, 115, 99, 114, 111, 119]}, {"kind": "account", "path": "base"}]}}, {"name": "escrow_token", "docs": ["<PERSON><PERSON><PERSON> Account."], "writable": true, "pda": {"seeds": [{"kind": "account", "path": "escrow"}, {"kind": "account", "path": "token_program"}, {"kind": "account", "path": "token_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "root_escrow_token", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "root_escrow"}, {"kind": "account", "path": "token_program"}, {"kind": "account", "path": "token_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "token_mint", "docs": ["Mint."], "relations": ["root_escrow"]}, {"name": "payer", "docs": ["Rent Payer"], "writable": true, "signer": true}, {"name": "recipient"}, {"name": "system_program", "docs": ["system program."], "address": "******************************11"}, {"name": "token_program", "docs": ["Token program."]}, {"name": "associated_token_program", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "CreateVestingEscrowFromRootParams"}}}, {"name": "proof", "type": {"vec": {"array": ["u8", 32]}}}, {"name": "remaining_accounts_info", "type": {"option": {"defined": {"name": "RemainingAccountsInfo"}}}}]}, {"name": "create_vesting_escrow_metadata", "docs": ["Create vesting escrow metadata", "# Arguments", "", "* ctx - The accounts needed by instruction.", "* params - The params needed by instruction.", "* name - The name of the vesting escrow", "* description - The description of the vesting escrow", "* creator_email - The email of the creator", "* recipient_email - The email of the recipient", ""], "discriminator": [93, 78, 33, 103, 173, 125, 70, 0], "accounts": [{"name": "escrow", "docs": ["The [Escrow]."], "writable": true}, {"name": "creator", "docs": ["Creator of the escrow."], "signer": true, "relations": ["escrow"]}, {"name": "escrow_metadata", "docs": ["The [ProposalMeta]."], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [101, 115, 99, 114, 111, 119, 95, 109, 101, 116, 97, 100, 97, 116, 97]}, {"kind": "account", "path": "escrow"}]}}, {"name": "payer", "docs": ["Payer of the [ProposalMeta]."], "writable": true, "signer": true}, {"name": "system_program", "docs": ["system program."], "address": "******************************11"}], "args": [{"name": "params", "type": {"defined": {"name": "CreateVestingEscrowMetadataParameters"}}}]}, {"name": "create_vesting_escrow_v2", "docs": ["Create a vesting escrow for the given params", "This instruction supports both splToken and token2022", "# Arguments", "", "* ctx - The accounts needed by instruction.", "* params - The params needed by instruction.", "* vesting_start_time - The creation time of this escrow", "* cliff_time - Trade cliff time of the escrow", "* frequency - How frequent the claimable amount will be updated", "* cliff_unlock_amount - The amount unlocked after cliff time", "* amount_per_period - The amount unlocked per vesting period", "* number_of_period - The total number of vesting period", "* update_recipient_mode - <PERSON>ide who can update the recipient of the escrow", "* cancel_mode - Decide who can cancel the the escrow", "* remaining_accounts_info: additional accounts needed by instruction", ""], "discriminator": [181, 155, 104, 183, 182, 128, 35, 47], "accounts": [{"name": "base", "docs": ["Base."], "writable": true, "signer": true}, {"name": "escrow", "docs": ["Escrow."], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [101, 115, 99, 114, 111, 119]}, {"kind": "account", "path": "base"}]}}, {"name": "token_mint"}, {"name": "escrow_token", "docs": ["<PERSON><PERSON><PERSON> Account."], "writable": true, "pda": {"seeds": [{"kind": "account", "path": "escrow"}, {"kind": "account", "path": "token_program"}, {"kind": "account", "path": "token_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "sender", "docs": ["Sender."], "writable": true, "signer": true}, {"name": "sender_token", "docs": ["Sender Token Account."], "writable": true}, {"name": "recipient"}, {"name": "token_program", "docs": ["Token program."]}, {"name": "system_program", "docs": ["system program."], "address": "******************************11"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "params", "type": {"defined": {"name": "CreateVestingEscrowParameters"}}}, {"name": "remaining_accounts_info", "type": {"option": {"defined": {"name": "RemainingAccountsInfo"}}}}]}, {"name": "fund_root_escrow", "docs": ["Fund root escrow"], "discriminator": [251, 106, 189, 200, 108, 15, 144, 95], "accounts": [{"name": "root_escrow", "docs": ["Root Escrow."], "writable": true}, {"name": "token_mint", "relations": ["root_escrow"]}, {"name": "root_escrow_token", "docs": ["<PERSON><PERSON><PERSON> Account."], "writable": true, "pda": {"seeds": [{"kind": "account", "path": "root_escrow"}, {"kind": "account", "path": "token_program"}, {"kind": "account", "path": "token_mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "payer", "docs": ["Payer."], "writable": true, "signer": true}, {"name": "payer_token", "docs": ["Payer <PERSON> Account."], "writable": true}, {"name": "token_program", "docs": ["Token program."]}, {"name": "system_program", "docs": ["system program."], "address": "******************************11"}, {"name": "associated_token_program", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "max_amount", "type": "u64"}, {"name": "remaining_accounts_info", "type": {"option": {"defined": {"name": "RemainingAccountsInfo"}}}}]}, {"name": "update_vesting_escrow_recipient", "docs": ["Update vesting escrow metadata", "# Arguments", "", "* ctx - The accounts needed by instruction.", "* new_recipient - The address of the new recipient", "* new_recipient_email - The email of the new recipient", ""], "discriminator": [26, 242, 127, 255, 237, 109, 47, 206], "accounts": [{"name": "escrow", "docs": ["Escrow."], "writable": true}, {"name": "escrow_metadata", "docs": ["Escrow metadata."], "writable": true, "optional": true}, {"name": "signer", "docs": ["Signer."], "writable": true, "signer": true}, {"name": "system_program", "docs": ["system program."], "address": "******************************11"}, {"name": "event_authority", "pda": {"seeds": [{"kind": "const", "value": [95, 95, 101, 118, 101, 110, 116, 95, 97, 117, 116, 104, 111, 114, 105, 116, 121]}]}}, {"name": "program"}], "args": [{"name": "new_recipient", "type": "pubkey"}, {"name": "new_recipient_email", "type": {"option": "string"}}]}], "accounts": [{"name": "RootEscrow", "discriminator": [253, 209, 220, 107, 206, 191, 71, 158]}, {"name": "VestingEscrow", "discriminator": [244, 119, 183, 4, 73, 116, 135, 195]}, {"name": "VestingEscrowMetadata", "discriminator": [24, 204, 166, 104, 87, 158, 76, 13]}], "events": [{"name": "EventCancelVestingEscrow", "discriminator": [113, 2, 117, 173, 195, 39, 101, 155]}, {"name": "EventCancelVestingEscrowV3", "discriminator": [41, 143, 236, 79, 116, 120, 91, 143]}, {"name": "EventClaim", "discriminator": [171, 144, 1, 189, 120, 200, 38, 11]}, {"name": "EventClaimV3", "discriminator": [229, 197, 142, 10, 41, 122, 171, 154]}, {"name": "EventCloseClaimStatus", "discriminator": [87, 68, 38, 194, 241, 155, 125, 107]}, {"name": "EventCloseVestingEscrow", "discriminator": [45, 141, 253, 209, 196, 133, 21, 204]}, {"name": "EventCreateRootEscrow", "discriminator": [105, 216, 97, 182, 27, 224, 199, 228]}, {"name": "EventCreateVestingEscrow", "discriminator": [248, 222, 89, 61, 170, 208, 131, 117]}, {"name": "EventFundRootEscrow", "discriminator": [74, 8, 68, 181, 198, 235, 138, 81]}, {"name": "EventUpdateVestingEscrowRecipient", "discriminator": [206, 218, 33, 65, 133, 237, 131, 57]}], "errors": [{"code": 6000, "name": "MathOverflow", "msg": "Math operation overflow"}, {"code": 6001, "name": "FrequencyIsZero", "msg": "Frequency is zero"}, {"code": 6002, "name": "InvalidEscrowTokenAddress", "msg": "Invalid escrow token address"}, {"code": 6003, "name": "InvalidUpdateRecipientMode", "msg": "Invalid update recipient mode"}, {"code": 6004, "name": "InvalidCancelMode", "msg": "Invalid cancel mode"}, {"code": 6005, "name": "NotPermitToDoThisAction", "msg": "Not permit to do this action"}, {"code": 6006, "name": "InvalidRecipientTokenAccount", "msg": "Invalid recipient token account"}, {"code": 6007, "name": "InvalidCreatorTokenAccount", "msg": "Invalid creator token account"}, {"code": 6008, "name": "InvalidEscrowMetadata", "msg": "Invalid escrow metadata"}, {"code": 6009, "name": "InvalidVestingStartTime", "msg": "Invalid vesting start time"}, {"code": 6010, "name": "AlreadyCancelled", "msg": "Already cancelled"}, {"code": 6011, "name": "CancelledAtIsZero", "msg": "Cancelled timestamp is zero"}, {"code": 6012, "name": "IncorrectTokenProgramId", "msg": "Invalid token program ID"}, {"code": 6013, "name": "TransferFeeCalculationFailure", "msg": "Calculate transfer fee failure"}, {"code": 6014, "name": "UnsupportedMint", "msg": "Unsupported mint"}, {"code": 6015, "name": "InvalidRemainingAccountSlice", "msg": "Invalid remaining accounts"}, {"code": 6016, "name": "InsufficientRemainingAccounts", "msg": "Insufficient remaining accounts"}, {"code": 6017, "name": "DuplicatedRemainingAccountTypes", "msg": "Same accounts type is provided more than once"}, {"code": 6018, "name": "NoTransferHookProgram", "msg": "Missing remaining accounts for transfer hook."}, {"code": 6019, "name": "ClaimingIsNotFinished", "msg": "Claiming is not finished"}, {"code": 6020, "name": "InvalidMerkleProof", "msg": "Invalid merkle proof"}, {"code": 6021, "name": "EscrowNotCancelled", "msg": "Escrow is not cancelled"}, {"code": 6022, "name": "AmountIsZero", "msg": "Amount is zero"}, {"code": 6023, "name": "InvalidParams", "msg": "Invalid params"}], "types": [{"name": "AccountsType", "type": {"kind": "enum", "variants": [{"name": "TransferHookEscrow"}]}}, {"name": "CreateRootEscrowParameters", "type": {"kind": "struct", "fields": [{"name": "max_claim_amount", "type": "u64"}, {"name": "max_escrow", "type": "u64"}, {"name": "version", "type": "u64"}, {"name": "root", "type": {"array": ["u8", 32]}}]}}, {"name": "CreateVestingEscrowFromRootParams", "type": {"kind": "struct", "fields": [{"name": "vesting_start_time", "type": "u64"}, {"name": "cliff_time", "type": "u64"}, {"name": "frequency", "type": "u64"}, {"name": "cliff_unlock_amount", "type": "u64"}, {"name": "amount_per_period", "type": "u64"}, {"name": "number_of_period", "type": "u64"}, {"name": "update_recipient_mode", "type": "u8"}, {"name": "cancel_mode", "type": "u8"}]}}, {"name": "CreateVestingEscrowMetadataParameters", "type": {"kind": "struct", "fields": [{"name": "name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "creator_email", "type": "string"}, {"name": "recipient_email", "type": "string"}]}}, {"name": "CreateVestingEscrowParameters", "docs": ["Accounts for [locker::create_vesting_escrow]."], "type": {"kind": "struct", "fields": [{"name": "vesting_start_time", "type": "u64"}, {"name": "cliff_time", "type": "u64"}, {"name": "frequency", "type": "u64"}, {"name": "cliff_unlock_amount", "type": "u64"}, {"name": "amount_per_period", "type": "u64"}, {"name": "number_of_period", "type": "u64"}, {"name": "update_recipient_mode", "type": "u8"}, {"name": "cancel_mode", "type": "u8"}]}}, {"name": "EventCancelVestingEscrow", "type": {"kind": "struct", "fields": [{"name": "escrow", "type": "pubkey"}, {"name": "signer", "type": "pubkey"}, {"name": "claimable_amount", "type": "u64"}, {"name": "remaining_amount", "type": "u64"}, {"name": "cancelled_at", "type": "u64"}]}}, {"name": "EventCancelVestingEscrowV3", "type": {"kind": "struct", "fields": [{"name": "escrow", "type": "pubkey"}, {"name": "signer", "type": "pubkey"}, {"name": "remaining_amount", "type": "u64"}, {"name": "cancelled_at", "type": "u64"}]}}, {"name": "EventClaim", "type": {"kind": "struct", "fields": [{"name": "amount", "type": "u64"}, {"name": "current_ts", "type": "u64"}, {"name": "escrow", "type": "pubkey"}]}}, {"name": "EventClaimV3", "type": {"kind": "struct", "fields": [{"name": "amount", "type": "u64"}, {"name": "current_ts", "type": "u64"}, {"name": "escrow", "type": "pubkey"}, {"name": "vesting_start_time", "type": "u64"}, {"name": "cliff_time", "type": "u64"}, {"name": "frequency", "type": "u64"}, {"name": "cliff_unlock_amount", "type": "u64"}, {"name": "amount_per_period", "type": "u64"}, {"name": "number_of_period", "type": "u64"}, {"name": "recipient", "type": "pubkey"}]}}, {"name": "EventCloseClaimStatus", "type": {"kind": "struct", "fields": [{"name": "escrow", "type": "pubkey"}, {"name": "recipient", "type": "pubkey"}, {"name": "rent_receiver", "type": "pubkey"}]}}, {"name": "EventCloseVestingEscrow", "type": {"kind": "struct", "fields": [{"name": "escrow", "type": "pubkey"}]}}, {"name": "EventCreateRootEscrow", "type": {"kind": "struct", "fields": [{"name": "root_escrow", "type": "pubkey"}, {"name": "max_claim_amount", "type": "u64"}, {"name": "max_escrow", "type": "u64"}, {"name": "version", "type": "u64"}, {"name": "root", "type": {"array": ["u8", 32]}}]}}, {"name": "EventCreateVestingEscrow", "type": {"kind": "struct", "fields": [{"name": "vesting_start_time", "type": "u64"}, {"name": "cliff_time", "type": "u64"}, {"name": "frequency", "type": "u64"}, {"name": "cliff_unlock_amount", "type": "u64"}, {"name": "amount_per_period", "type": "u64"}, {"name": "number_of_period", "type": "u64"}, {"name": "update_recipient_mode", "type": "u8"}, {"name": "cancel_mode", "type": "u8"}, {"name": "recipient", "type": "pubkey"}, {"name": "escrow", "type": "pubkey"}]}}, {"name": "EventFundRootEscrow", "type": {"kind": "struct", "fields": [{"name": "root_escrow", "type": "pubkey"}, {"name": "funded_amount", "type": "u64"}]}}, {"name": "EventUpdateVestingEscrowRecipient", "type": {"kind": "struct", "fields": [{"name": "escrow", "type": "pubkey"}, {"name": "old_recipient", "type": "pubkey"}, {"name": "new_recipient", "type": "pubkey"}, {"name": "signer", "type": "pubkey"}]}}, {"name": "RemainingAccountsInfo", "type": {"kind": "struct", "fields": [{"name": "slices", "type": {"vec": {"defined": {"name": "RemainingAccountsSlice"}}}}]}}, {"name": "RemainingAccountsSlice", "type": {"kind": "struct", "fields": [{"name": "accounts_type", "type": {"defined": {"name": "AccountsType"}}}, {"name": "length", "type": "u8"}]}}, {"name": "RootEscrow", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "token_mint", "docs": ["token mint"], "type": "pubkey"}, {"name": "creator", "docs": ["creator of the escrow"], "type": "pubkey"}, {"name": "base", "docs": ["escrow base key"], "type": "pubkey"}, {"name": "root", "docs": ["256 bit merkle root"], "type": {"array": ["u8", 32]}}, {"name": "bump", "docs": ["bump"], "type": "u8"}, {"name": "token_program_flag", "docs": ["token program flag"], "type": "u8"}, {"name": "padding_0", "docs": ["padding"], "type": {"array": ["u8", 6]}}, {"name": "max_claim_amount", "docs": ["max claim amount"], "type": "u64"}, {"name": "max_escrow", "docs": ["max escrow"], "type": "u64"}, {"name": "total_funded_amount", "docs": ["total funded amount"], "type": "u64"}, {"name": "total_escrow_created", "docs": ["total escrow created"], "type": "u64"}, {"name": "total_distribute_amount", "docs": ["total distributed amount"], "type": "u64"}, {"name": "version", "docs": ["version"], "type": "u64"}, {"name": "padding", "docs": ["padding"], "type": "u64"}, {"name": "buffer", "docs": ["buffer"], "type": {"array": ["u128", 5]}}]}}, {"name": "VestingEscrow", "serialization": "bytemuck", "repr": {"kind": "c"}, "type": {"kind": "struct", "fields": [{"name": "recipient", "docs": ["recipient address"], "type": "pubkey"}, {"name": "token_mint", "docs": ["token mint"], "type": "pubkey"}, {"name": "creator", "docs": ["creator of the escrow"], "type": "pubkey"}, {"name": "base", "docs": ["escrow base key"], "type": "pubkey"}, {"name": "escrow_bump", "docs": ["escrow bump"], "type": "u8"}, {"name": "update_recipient_mode", "docs": ["update_recipient_mode"], "type": "u8"}, {"name": "cancel_mode", "docs": ["cancel_mode"], "type": "u8"}, {"name": "token_program_flag", "docs": ["token program flag"], "type": "u8"}, {"name": "padding_0", "docs": ["padding"], "type": {"array": ["u8", 4]}}, {"name": "cliff_time", "docs": ["cliff time"], "type": "u64"}, {"name": "frequency", "docs": ["frequency"], "type": "u64"}, {"name": "cliff_unlock_amount", "docs": ["cliff unlock amount"], "type": "u64"}, {"name": "amount_per_period", "docs": ["amount per period"], "type": "u64"}, {"name": "number_of_period", "docs": ["number of period"], "type": "u64"}, {"name": "total_claimed_amount", "docs": ["total claimed amount"], "type": "u64"}, {"name": "vesting_start_time", "docs": ["vesting start time"], "type": "u64"}, {"name": "cancelled_at", "docs": ["cancelled_at"], "type": "u64"}, {"name": "padding_1", "docs": ["buffer"], "type": "u64"}, {"name": "buffer", "docs": ["buffer"], "type": {"array": ["u128", 5]}}]}}, {"name": "VestingEscrowMetadata", "docs": ["Metada<PERSON> about an escrow."], "type": {"kind": "struct", "fields": [{"name": "escrow", "docs": ["The [Escrow]."], "type": "pubkey"}, {"name": "name", "docs": ["Name of escrow."], "type": "string"}, {"name": "description", "docs": ["Description of escrow."], "type": "string"}, {"name": "creator_email", "docs": ["Email of creator"], "type": "string"}, {"name": "recipient_email", "docs": ["Email of recipient"], "type": "string"}]}}]}