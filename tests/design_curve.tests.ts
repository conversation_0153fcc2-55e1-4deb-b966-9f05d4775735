import { BanksClient, ProgramTestContext } from "solana-bankrun";
import {
    createConfig,
    CreateConfigParams,
    createLocker,
    createMeteoraMetadata,
    createPoolWithSplToken,
    MigrateMeteoraParams,
    migrateToMeteoraDamm,
    swap,
    SwapParams,
} from "./instructions";
import { VirtualCurveProgram } from "./utils/types";
import { Keypair, PublicKey } from "@solana/web3.js";
import { createDammConfig, designCurve, fundSol, getMint, startTest } from "./utils";
import {
    createVirtualCurveProgram,
    derivePoolAuthority,
} from "./utils";
import { getConfig, getVirtualPool } from "./utils/fetcher";

import { expect } from "chai";
import { createToken, mintSplTokenTo } from "./utils/token";
import { BN } from "bn.js";

describe("Design default curve", () => {
    let context: ProgramTestContext;
    let admin: Keypair;
    let operator: Keypair;
    let partner: Keypair;
    let user: Keypair;
    let poolCreator: Keypair;
    let program: VirtualCurveProgram;

    before(async () => {
        context = await startTest();
        admin = context.payer;
        operator = Keypair.generate();
        partner = Keypair.generate();
        user = Keypair.generate();
        poolCreator = Keypair.generate();
        const receivers = [
            operator.publicKey,
            partner.publicKey,
            user.publicKey,
            poolCreator.publicKey,
        ];
        await fundSol(context.banksClient, admin, receivers);
        program = createVirtualCurveProgram();
    });

    it("Design curve with lock vesting", async () => {
        let totalTokenSupply = 1_000_000_000; // 1 billion
        let percentageSupplyOnMigration = 10; // 10%;
        let migrationQuoteThreshold = 300; // 300 sol
        let tokenBaseDecimal = 6;
        let tokenQuoteDecimal = 9;
        let lockedVesting = {
            amountPerPeriod: new BN(123456),
            cliffDurationFromMigrationTime: new BN(0),
            frequency: new BN(1),
            numberOfPeriod: new BN(120),
            cliffUnlockAmount: new BN(123456),
        };
        let migrationOption = 0;
        let quoteMint = await createToken(context.banksClient, admin, admin.publicKey, tokenQuoteDecimal);
        let instructionParams = designCurve(
            totalTokenSupply,
            percentageSupplyOnMigration,
            migrationQuoteThreshold,
            migrationOption,
            tokenBaseDecimal,
            tokenQuoteDecimal,
            0,
            1,
            lockedVesting
        );
        const params: CreateConfigParams = {
            payer: partner,
            leftoverReceiver: partner.publicKey,
            feeClaimer: partner.publicKey,
            quoteMint,
            instructionParams,
        };
        let config = await createConfig(context.banksClient, program, params);
        await mintSplTokenTo(context.banksClient, user, quoteMint, admin, user.publicKey, instructionParams.migrationQuoteThreshold.toNumber());
        await fullFlow(context.banksClient, program, config, operator, poolCreator, user, admin, quoteMint);
    });

    it("Design curve without lock vesting", async () => {
        let totalTokenSupply = 1_000_000_000; // 1 billion
        let percentageSupplyOnMigration = 10; // 10%;
        let migrationQuoteThreshold = 300; // 300 sol
        let migrationOption = 0;
        let tokenBaseDecimal = 6;
        let tokenQuoteDecimal = 9;
        let lockedVesting = {
            amountPerPeriod: new BN(0),
            cliffDurationFromMigrationTime: new BN(0),
            frequency: new BN(0),
            numberOfPeriod: new BN(0),
            cliffUnlockAmount: new BN(0),
        };
        let quoteMint = await createToken(context.banksClient, admin, admin.publicKey, tokenQuoteDecimal);
        let instructionParams = designCurve(
            totalTokenSupply,
            percentageSupplyOnMigration,
            migrationQuoteThreshold,
            migrationOption,
            tokenBaseDecimal,
            tokenQuoteDecimal,
            0,
            1,
            lockedVesting
        );
        const params: CreateConfigParams = {
            payer: partner,
            leftoverReceiver: partner.publicKey,
            feeClaimer: partner.publicKey,
            quoteMint,
            instructionParams,
        };
        let config = await createConfig(context.banksClient, program, params);
        await mintSplTokenTo(context.banksClient, user, quoteMint, admin, user.publicKey, instructionParams.migrationQuoteThreshold.toNumber());
        await fullFlow(context.banksClient, program, config, operator, poolCreator, user, admin, quoteMint);
    });
});


async function fullFlow(
    banksClient: BanksClient,
    program: VirtualCurveProgram,
    config: PublicKey,
    operator: Keypair,
    poolCreator: Keypair,
    user: Keypair,
    admin: Keypair,
    quoteMint: PublicKey,
) {
    // create pool
    let virtualPool = await createPoolWithSplToken(banksClient, program, {
        poolCreator,
        payer: operator,
        quoteMint,
        config,
        instructionParams: {
            name: "test token spl",
            symbol: "TEST",
            uri: "abc.com",
        },
    });
    let virtualPoolState = await getVirtualPool(
        banksClient,
        program,
        virtualPool
    );

    let configState = await getConfig(banksClient, program, config);

    // swap
    const params: SwapParams = {
        config,
        payer: user,
        pool: virtualPool,
        inputTokenMint: quoteMint,
        outputTokenMint: virtualPoolState.baseMint,
        amountIn: configState.migrationQuoteThreshold,
        minimumAmountOut: new BN(0),
        referralTokenAccount: null,
    };
    await swap(banksClient, program, params);

    // migrate
    const poolAuthority = derivePoolAuthority();
    let dammConfig = await createDammConfig(
        banksClient,
        admin,
        poolAuthority
    );
    const migrationParams: MigrateMeteoraParams = {
        payer: admin,
        virtualPool,
        dammConfig,
    };
    await createMeteoraMetadata(banksClient, program, {
        payer: admin,
        virtualPool,
        config,
    });

    if (configState.lockedVestingConfig.frequency.toNumber() != 0) {
        await createLocker(banksClient, program, {
            payer: admin,
            virtualPool,
        });
    }
    await migrateToMeteoraDamm(banksClient, program, migrationParams);
    const baseMintData = (
        await getMint(banksClient, virtualPoolState.baseMint)
    );

    expect(baseMintData.supply.toString()).eq(configState.postMigrationTokenSupply.toString());

}
