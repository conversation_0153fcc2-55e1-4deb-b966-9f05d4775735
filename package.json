{"scripts": {"lint:fix": "prettier */*.js \"*/**/*{.js,.ts}\" -w", "lint": "prettier */*.js \"*/**/*{.js,.ts}\" --check", "test": "anchor build -p dynamic_bonding_curve -- --features local && yarn run ts-mocha --runInBand -p ./tsconfig.json -t 1000000 tests/**/*.tests.ts", "ltest": "anchor build -p dynamic_bonding_curve -- --features local && yarn run ts-mocha --runInBand -p ./tsconfig.json -t 1000000 tests/*.tests.ts", "ctest": "anchor build -p dynamic_bonding_curve -- --features local && yarn run ts-mocha --runInBand -p ./tsconfig.json -t 1000000 tests/backwards_compatibility/*.tests.ts"}, "dependencies": {"@coral-xyz/anchor": "^0.31.0", "@metaplex-foundation/mpl-token-metadata": "^3.4.0"}, "devDependencies": {"@coral-xyz/anchor-errors": "^0.31.0", "@solana/spl-token": "^0.4.8", "@solana/spl-token-metadata": "^0.1.6", "@solana/web3.js": "^1.95.3", "@types/bn.js": "^5.1.0", "@types/chai": "^4.3.0", "@types/mocha": "^9.0.0", "chai": "^4.3.4", "decimal.js": "^10.4.2", "mocha": "^9.0.3", "prettier": "^2.6.2", "solana-bankrun": "^0.4.0", "ts-mocha": "^10.0.0", "typescript": "^4.3.5", "tsx": "^4.20.3"}}